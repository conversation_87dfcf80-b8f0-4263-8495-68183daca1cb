Linux等保基线核查工具 - 故障排除指南
==========================================

如果程序"运行没反应"，请按以下步骤排查：

一、确认运行环境
1. 本程序需要在 Windows 系统上运行
2. 如果您在 Linux 系统上，请使用 mlps-cli 命令行版本

二、Windows环境故障排除

步骤1：运行调试版本
- 双击运行 mlps-debug.exe
- 如果有错误，会显示详细错误信息
- 程序会生成 debug.log 日志文件

步骤2：检查系统要求
- Windows 7/8/10/11 (64位)
- 确保没有被杀毒软件拦截
- 尝试以管理员身份运行

步骤3：查看日志文件
- 运行程序后查看 debug.log 文件
- 日志包含详细的启动过程信息

步骤4：测试基本功能
- 在调试版本中点击"文件" -> "测试功能"
- 验证核心功能是否正常

三、Linux环境解决方案

如果您在Linux环境下：
1. 编译命令行版本：
   go build -o mlps-cli simple_cli.go storage.go sshclient.go rules.go checker.go report.go

2. 运行命令行版本：
   ./mlps-cli

3. 命令行版本功能：
   - 主机管理
   - 连接测试  
   - 结果查看
   - 报告导出

四、常见问题解决

问题1：程序无法启动
解决：
- 检查是否为64位Windows系统
- 尝试在命令行运行查看错误信息
- 检查杀毒软件是否拦截

问题2：界面显示异常
解决：
- 确保系统支持GUI程序
- 检查显示设置和DPI缩放
- 尝试兼容性模式运行

问题3：SSH连接失败
解决：
- 确认目标Linux主机网络可达
- 验证SSH服务是否运行
- 检查用户名密码是否正确
- 确认防火墙设置

五、获取技术支持

如果问题仍未解决：
1. 收集以下信息：
   - 操作系统版本
   - debug.log 文件内容
   - 错误截图或描述

2. 联系开发团队获取支持

六、文件说明

mlps-lite.exe     - 正式版GUI程序
mlps-debug.exe    - 调试版GUI程序（推荐故障排除时使用）
mlps-cli          - Linux命令行版本
simple_cli.go     - 命令行版本源码
debug.log         - 程序运行日志（运行后生成）

版本：v1.0
更新：2025-08-20
