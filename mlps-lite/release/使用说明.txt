Linux等保基线核查工具 v1.0 使用说明
========================================

一、快速开始
1. 双击运行 mlps-lite.exe
2. 首次运行会创建配置文件
3. 按照界面提示操作

二、主要功能
1. 主机管理 - 添加、编辑、删除Linux主机
2. 主机分组 - 创建分组，批量管理主机
3. 核查任务 - 执行等保基线检查
4. 核查结果 - 查看检查结果
5. 报告导出 - 生成TXT/CSV报告

三、内置规则（10条）
- 密码最小长度检查
- root远程登录检查  
- SSH密码登录检查
- 审计服务检查
- 文件权限检查
- SELinux状态检查
- 防火墙状态检查
- 自动更新检查
- 日志目录权限检查

四、注意事项
1. 确保能SSH连接到目标Linux主机
2. 建议使用具有sudo权限的用户
3. SSH凭据仅运行时使用，不会保存密码
4. 支持CentOS/RHEL、Ubuntu/Debian系统

五、技术支持
如有问题请联系开发团队

开发：Sec Team
日期：2025-08-20
版本：v1.0
