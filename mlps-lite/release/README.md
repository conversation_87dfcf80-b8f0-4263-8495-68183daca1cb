# Linux等保基线核查工具 (MLPS-Lite)

> 版本：v1.0 · 开发语言：Go · GUI框架：Walk · 日期：2025-08-20

## 项目简介

Linux等保基线核查工具是一款**在Windows上运行**的安全基线检查工具，通过SSH远程连接Linux主机，自动执行等保基线核查规则，生成详细的合规性报告。

### 主要特性

- ✅ **Windows原生GUI**：基于Walk框架的Windows原生界面
- ✅ **SSH远程检查**：支持密码和密钥认证
- ✅ **多主机管理**：支持主机分组和批量操作
- ✅ **内置规则库**：10条常见高优先级等保基线规则
- ✅ **并发执行**：可配置并发数，提高检查效率
- ✅ **多格式报告**：支持TXT、CSV、汇总报告导出
- ✅ **轻量设计**：无需数据库，使用JSON文件存储

## 系统要求

- **运行平台**：Windows 7/8/10/11 (64位)
- **目标主机**：Linux系统 (CentOS/RHEL、Ubuntu/Debian)
- **网络要求**：能够SSH连接到目标Linux主机

## 快速开始

### 1. 下载和运行

1. 下载 `mlps-lite.exe` 文件
2. 双击运行程序
3. 首次运行会在程序目录创建配置文件

### 2. 添加主机

1. 在左侧导航选择"主机管理" → "添加主机"
2. 填写主机信息：
   - 主机名称：自定义名称
   - IP地址：目标Linux主机IP
   - SSH端口：默认22
   - 用户名：SSH登录用户名
   - 认证方式：密码或私钥文件路径

### 3. 执行核查

1. 选择"核查任务" → "创建任务"
2. 选择要检查的主机或主机组
3. 选择要执行的规则（默认全选）
4. 设置并发数（建议2-5）
5. 点击"开始核查"

### 4. 查看结果

1. 选择"核查结果" → "结果查看"
2. 查看详细的检查结果
3. 可按主机、规则、状态筛选

### 5. 导出报告

1. 选择"核查结果" → "导出报告"
2. 选择报告格式：TXT、CSV、汇总
3. 选择保存位置

## 内置规则说明

| 规则ID | 规则名称 | 风险等级 | 说明 |
|--------|----------|----------|------|
| rule_001 | 密码最小长度检查 | 高 | 检查/etc/login.defs中PASS_MIN_LEN配置 |
| rule_002 | root远程登录检查 | 高 | 检查SSH是否禁止root远程登录 |
| rule_003 | SSH密码登录检查 | 中 | 检查SSH是否禁用密码登录 |
| rule_004 | 审计服务检查 | 高 | 检查auditd服务是否启用 |
| rule_005 | /etc/passwd权限检查 | 高 | 检查passwd文件权限是否为644 |
| rule_006 | /etc/shadow权限检查 | 高 | 检查shadow文件权限是否为600/640 |
| rule_007 | SELinux状态检查 | 中 | 检查SELinux是否启用 |
| rule_008 | 防火墙状态检查 | 高 | 检查防火墙是否启用 |
| rule_009 | 自动更新检查 | 中 | 检查是否配置自动更新 |
| rule_010 | 日志目录权限检查 | 中 | 检查/var/log目录权限 |

## 文件说明

程序运行后会在当前目录生成以下文件：

- `hosts.json` - 主机配置信息
- `groups.json` - 主机分组信息  
- `results.json` - 检查结果数据
- `executions.json` - 任务执行记录
- `等保基线核查报告_*.txt` - TXT格式报告
- `等保基线核查报告_*.csv` - CSV格式报告
- `等保基线核查汇总_*.txt` - 汇总报告

## 开发信息

### 技术栈

- **语言**：Go 1.23+
- **GUI框架**：Walk (Windows原生)
- **SSH库**：golang.org/x/crypto/ssh
- **存储**：JSON文件
- **编译**：Windows交叉编译

### 项目结构

```
mlps-lite/
├── main.go          # GUI主程序
├── storage.go       # 数据存储模块
├── sshclient.go     # SSH连接模块
├── rules.go         # 规则定义模块
├── checker.go       # 规则执行模块
├── report.go        # 报告生成模块
├── test_core.go     # 核心功能测试
└── README.md        # 说明文档
```

### 编译说明

在Linux环境下交叉编译Windows版本：

```bash
# 安装Go 1.23+
# 安装rsrc工具
go install github.com/akavel/rsrc@latest

# 生成资源文件
rsrc -manifest app.manifest -o rsrc.syso

# 交叉编译
GOOS=windows GOARCH=amd64 CGO_ENABLED=0 go build -ldflags="-H windowsgui" -o mlps-lite.exe
```

## 注意事项

1. **安全提醒**：SSH凭据仅在运行时使用，不会持久化存储密码
2. **网络要求**：确保Windows主机能够SSH连接到目标Linux主机
3. **权限要求**：建议使用具有sudo权限的用户进行检查
4. **防火墙**：确保目标主机SSH端口（默认22）可访问
5. **兼容性**：优先支持CentOS/RHEL和Ubuntu/Debian系统

## 故障排除

### 常见问题

1. **连接失败**
   - 检查IP地址和端口是否正确
   - 确认SSH服务是否运行
   - 验证用户名和密码/密钥

2. **规则检查失败**
   - 确认用户具有足够权限
   - 检查目标系统是否支持相关命令

3. **报告生成失败**
   - 确认有写入权限
   - 检查磁盘空间是否充足

### 日志查看

程序运行时的错误信息会显示在界面上，如需详细调试，可以在命令行运行程序查看详细输出。

## 更新日志

### v1.0 (2025-08-20)
- ✅ 初始版本发布
- ✅ 实现基础GUI界面
- ✅ 支持SSH远程连接
- ✅ 内置10条等保基线规则
- ✅ 支持多格式报告导出
- ✅ 实现主机和分组管理

## 技术支持

如有问题或建议，请联系开发团队。

---

**Linux等保基线核查工具 v1.0** - 让等保合规检查更简单高效！
