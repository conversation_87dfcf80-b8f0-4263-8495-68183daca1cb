package main

import (
	"fmt"
	"strconv"

	"github.com/lxn/walk"
	. "github.com/lxn/walk/declarative"
)

type HostListModel struct {
	walk.TableModelBase
	hosts []Host
}

func NewHostListModel() *HostListModel {
	return &HostListModel{}
}

func (m *HostListModel) RowCount() int {
	return len(m.hosts)
}

func (m *HostListModel) Value(row, col int) interface{} {
	if row >= len(m.hosts) {
		return nil
	}
	
	host := m.hosts[row]
	switch col {
	case 0:
		return host.Name
	case 1:
		return host.IP
	case 2:
		return host.Port
	case 3:
		return host.Username
	case 4:
		return host.OS
	case 5:
		return host.Status
	}
	return nil
}

func (m *HostListModel) SetHosts(hosts []Host) {
	m.hosts = hosts
	m.PublishRowsReset()
}

func (mw *AppMainWindow) showHostListWindow() {
	var hostTable *walk.TableView
	hostModel := NewHostListModel()
	
	// 加载主机数据
	hosts, err := mw.storage.LoadHosts()
	if err == nil {
		hostModel.SetHosts(hosts)
	}
	
	var dlg *walk.Dialog

	Dialog{
		AssignTo: &dlg,
		Title:    "主机管理 - 主机列表",
		MinSize:  Size{800, 600},
		Layout:   VBox{},
		Children: []Widget{
			Composite{
				Layout: HBox{},
				Children: []Widget{
					PushButton{
						Text: "添加主机",
						OnClicked: func() {
							mw.showAddHostDialog()
							mw.refreshHostList(hostModel)
						},
					},
					PushButton{
						Text: "编辑主机",
						OnClicked: func() {
							if hostTable.CurrentIndex() >= 0 {
								mw.showEditHostDialog(hostModel.hosts[hostTable.CurrentIndex()])
								mw.refreshHostList(hostModel)
							} else {
								walk.MsgBox(dlg, "提示", "请先选择要编辑的主机", walk.MsgBoxIconInformation)
							}
						},
					},
					PushButton{
						Text: "删除主机",
						OnClicked: func() {
							if hostTable.CurrentIndex() >= 0 {
								host := hostModel.hosts[hostTable.CurrentIndex()]
								result := walk.MsgBox(dlg, "确认删除",
									fmt.Sprintf("确定要删除主机 %s (%s) 吗？", host.Name, host.IP),
									walk.MsgBoxYesNo|walk.MsgBoxIconQuestion)
								if result == walk.DlgCmdYes {
									mw.deleteHost(host.ID, hostModel)
								}
							} else {
								walk.MsgBox(dlg, "提示", "请先选择要删除的主机", walk.MsgBoxIconInformation)
							}
						},
					},
					PushButton{
						Text: "测试连接",
						OnClicked: func() {
							if hostTable.CurrentIndex() >= 0 {
								host := hostModel.hosts[hostTable.CurrentIndex()]
								mw.testHostConnection(host)
							} else {
								walk.MsgBox(dlg, "提示", "请先选择要测试的主机", walk.MsgBoxIconInformation)
							}
						},
					},
					PushButton{
						Text: "刷新列表",
						OnClicked: func() {
							mw.refreshHostList(hostModel)
						},
					},
				},
			},
			TableView{
				AssignTo: &hostTable,
				Model:    hostModel,
				Columns: []TableViewColumn{
					{Title: "主机名称", Width: 120},
					{Title: "IP地址", Width: 120},
					{Title: "端口", Width: 60},
					{Title: "用户名", Width: 80},
					{Title: "操作系统", Width: 120},
					{Title: "状态", Width: 80},
				},
			},
		},
	}.Run(mw.MainWindow)
}

func (mw *AppMainWindow) showAddHostDialog() {
	var dlg *walk.Dialog
	var nameEdit, ipEdit, portEdit, usernameEdit, passwordEdit, keyPathEdit *walk.LineEdit
	var osEdit *walk.LineEdit
	
	Dialog{
		AssignTo: &dlg,
		Title:    "添加主机",
		MinSize:  Size{400, 300},
		Layout:   VBox{},
		Children: []Widget{
			Composite{
				Layout: Grid{Columns: 2},
				Children: []Widget{
					Label{Text: "主机名称:"},
					LineEdit{AssignTo: &nameEdit},
					
					Label{Text: "IP地址:"},
					LineEdit{AssignTo: &ipEdit},
					
					Label{Text: "SSH端口:"},
					LineEdit{AssignTo: &portEdit, Text: "22"},
					
					Label{Text: "用户名:"},
					LineEdit{AssignTo: &usernameEdit},
					
					Label{Text: "密码:"},
					LineEdit{AssignTo: &passwordEdit, PasswordMode: true},
					
					Label{Text: "私钥路径:"},
					LineEdit{AssignTo: &keyPathEdit},
					
					Label{Text: "操作系统:"},
					LineEdit{AssignTo: &osEdit, Text: "Linux"},
				},
			},
			Composite{
				Layout: HBox{},
				Children: []Widget{
					HSpacer{},
					PushButton{
						Text: "确定",
						OnClicked: func() {
							if nameEdit.Text() == "" || ipEdit.Text() == "" || usernameEdit.Text() == "" {
								walk.MsgBox(dlg, "错误", "请填写必要信息（主机名称、IP地址、用户名）", walk.MsgBoxIconError)
								return
							}
							
							port, err := strconv.Atoi(portEdit.Text())
							if err != nil {
								port = 22
							}
							
							host := Host{
								Name:     nameEdit.Text(),
								IP:       ipEdit.Text(),
								Port:     port,
								Username: usernameEdit.Text(),
								Password: passwordEdit.Text(),
								KeyPath:  keyPathEdit.Text(),
								OS:       osEdit.Text(),
								Status:   "未连接",
							}
							
							err = mw.storage.AddHost(host)
							if err != nil {
								walk.MsgBox(dlg, "错误", fmt.Sprintf("添加主机失败: %v", err), walk.MsgBoxIconError)
								return
							}
							
							walk.MsgBox(dlg, "成功", "主机添加成功", walk.MsgBoxIconInformation)
							dlg.Accept()
						},
					},
					PushButton{
						Text: "取消",
						OnClicked: func() {
							dlg.Cancel()
						},
					},
				},
			},
		},
	}.Run(mw.MainWindow)
}

func (mw *AppMainWindow) showEditHostDialog(host Host) {
	var dlg *walk.Dialog
	var nameEdit, ipEdit, portEdit, usernameEdit, passwordEdit, keyPathEdit *walk.LineEdit
	var osEdit *walk.LineEdit
	
	Dialog{
		AssignTo: &dlg,
		Title:    "编辑主机",
		MinSize:  Size{400, 300},
		Layout:   VBox{},
		Children: []Widget{
			Composite{
				Layout: Grid{Columns: 2},
				Children: []Widget{
					Label{Text: "主机名称:"},
					LineEdit{AssignTo: &nameEdit, Text: host.Name},
					
					Label{Text: "IP地址:"},
					LineEdit{AssignTo: &ipEdit, Text: host.IP},
					
					Label{Text: "SSH端口:"},
					LineEdit{AssignTo: &portEdit, Text: strconv.Itoa(host.Port)},
					
					Label{Text: "用户名:"},
					LineEdit{AssignTo: &usernameEdit, Text: host.Username},
					
					Label{Text: "密码:"},
					LineEdit{AssignTo: &passwordEdit, PasswordMode: true, Text: host.Password},
					
					Label{Text: "私钥路径:"},
					LineEdit{AssignTo: &keyPathEdit, Text: host.KeyPath},
					
					Label{Text: "操作系统:"},
					LineEdit{AssignTo: &osEdit, Text: host.OS},
				},
			},
			Composite{
				Layout: HBox{},
				Children: []Widget{
					HSpacer{},
					PushButton{
						Text: "确定",
						OnClicked: func() {
							if nameEdit.Text() == "" || ipEdit.Text() == "" || usernameEdit.Text() == "" {
								walk.MsgBox(dlg, "错误", "请填写必要信息（主机名称、IP地址、用户名）", walk.MsgBoxIconError)
								return
							}
							
							port, err := strconv.Atoi(portEdit.Text())
							if err != nil {
								port = 22
							}
							
							updatedHost := host
							updatedHost.Name = nameEdit.Text()
							updatedHost.IP = ipEdit.Text()
							updatedHost.Port = port
							updatedHost.Username = usernameEdit.Text()
							updatedHost.Password = passwordEdit.Text()
							updatedHost.KeyPath = keyPathEdit.Text()
							updatedHost.OS = osEdit.Text()
							
							err = mw.storage.AddHost(updatedHost)
							if err != nil {
								walk.MsgBox(dlg, "错误", fmt.Sprintf("更新主机失败: %v", err), walk.MsgBoxIconError)
								return
							}
							
							walk.MsgBox(dlg, "成功", "主机更新成功", walk.MsgBoxIconInformation)
							dlg.Accept()
						},
					},
					PushButton{
						Text: "取消",
						OnClicked: func() {
							dlg.Cancel()
						},
					},
				},
			},
		},
	}.Run(mw.MainWindow)
}

func (mw *AppMainWindow) deleteHost(hostID string, model *HostListModel) {
	err := mw.storage.DeleteHost(hostID)
	if err != nil {
		walk.MsgBox(mw.MainWindow, "错误", fmt.Sprintf("删除主机失败: %v", err), walk.MsgBoxIconError)
		return
	}
	
	walk.MsgBox(mw.MainWindow, "成功", "主机删除成功", walk.MsgBoxIconInformation)
	mw.refreshHostList(model)
}

func (mw *AppMainWindow) testHostConnection(host Host) {
	go func() {
		err := TestHostConnection(&host)
		
		// 更新主机状态
		if err == nil {
			host.Status = "连接正常"
		} else {
			host.Status = "连接失败"
		}
		mw.storage.AddHost(host)
		
		// 在UI线程中显示结果
		mw.MainWindow.Synchronize(func() {
			if err == nil {
				walk.MsgBox(mw.MainWindow, "连接测试", 
					fmt.Sprintf("主机 %s (%s) 连接成功", host.Name, host.IP), 
					walk.MsgBoxIconInformation)
			} else {
				walk.MsgBox(mw.MainWindow, "连接测试", 
					fmt.Sprintf("主机 %s (%s) 连接失败:\n%v", host.Name, host.IP, err), 
					walk.MsgBoxIconError)
			}
		})
	}()
}

func (mw *AppMainWindow) refreshHostList(model *HostListModel) {
	hosts, err := mw.storage.LoadHosts()
	if err != nil {
		walk.MsgBox(mw.MainWindow, "错误", fmt.Sprintf("加载主机列表失败: %v", err), walk.MsgBoxIconError)
		return
	}

	model.SetHosts(hosts)
}

func (mw *AppMainWindow) showCreateTaskDialog() {
	walk.MsgBox(mw.MainWindow, "创建任务", "核查任务功能开发中...\n\n将支持：\n• 选择主机或分组\n• 选择检查规则\n• 设置并发数\n• 执行检查任务", walk.MsgBoxIconInformation)
}

func (mw *AppMainWindow) showTaskHistoryWindow() {
	executions, err := mw.storage.LoadExecutions()
	if err != nil {
		walk.MsgBox(mw.MainWindow, "错误", fmt.Sprintf("加载任务历史失败: %v", err), walk.MsgBoxIconError)
		return
	}

	if len(executions) == 0 {
		walk.MsgBox(mw.MainWindow, "任务历史", "暂无执行记录", walk.MsgBoxIconInformation)
		return
	}

	var message string
	message = fmt.Sprintf("任务历史记录（共%d条）：\n\n", len(executions))
	for i, exec := range executions {
		if i < 5 {
			message += fmt.Sprintf("• %s - %s (%s)\n", exec.Name, exec.Status, exec.StartTime)
		}
	}
	if len(executions) > 5 {
		message += fmt.Sprintf("... 还有%d条记录", len(executions)-5)
	}

	walk.MsgBox(mw.MainWindow, "任务历史", message, walk.MsgBoxIconInformation)
}

func (mw *AppMainWindow) showResultsWindow() {
	results, err := mw.storage.LoadResults()
	if err != nil {
		walk.MsgBox(mw.MainWindow, "错误", fmt.Sprintf("加载检查结果失败: %v", err), walk.MsgBoxIconError)
		return
	}

	if len(results) == 0 {
		walk.MsgBox(mw.MainWindow, "检查结果", "暂无检查结果", walk.MsgBoxIconInformation)
		return
	}

	passCount := 0
	failCount := 0
	errorCount := 0

	for _, result := range results {
		switch result.Status {
		case "PASS":
			passCount++
		case "FAIL":
			failCount++
		case "ERROR":
			errorCount++
		}
	}

	message := fmt.Sprintf("检查结果统计：\n\n总计：%d 项\n通过：%d 项\n失败：%d 项\n错误：%d 项",
		len(results), passCount, failCount, errorCount)

	walk.MsgBox(mw.MainWindow, "检查结果", message, walk.MsgBoxIconInformation)
}

func (mw *AppMainWindow) showExportReportDialog() {
	executions, err := mw.storage.LoadExecutions()
	if err != nil {
		walk.MsgBox(mw.MainWindow, "错误", fmt.Sprintf("加载执行记录失败: %v", err), walk.MsgBoxIconError)
		return
	}

	if len(executions) == 0 {
		walk.MsgBox(mw.MainWindow, "导出报告", "暂无可导出的报告数据", walk.MsgBoxIconInformation)
		return
	}

	// 使用最新的执行记录生成报告
	latestExecution := executions[len(executions)-1]

	txtFile, err := mw.reporter.GenerateReport(latestExecution.ID, "txt", ".")
	if err != nil {
		walk.MsgBox(mw.MainWindow, "错误", fmt.Sprintf("生成报告失败: %v", err), walk.MsgBoxIconError)
		return
	}

	walk.MsgBox(mw.MainWindow, "导出报告", fmt.Sprintf("报告导出成功：\n%s", txtFile), walk.MsgBoxIconInformation)
}
