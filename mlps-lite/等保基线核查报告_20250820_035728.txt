Linux等保基线核查报告
===========================================

执行ID: test-execution-1
任务名称: 测试执行任务
生成时间: 2025-08-20 03:57:28
检查主机数: 1
检查规则数: 2
通过项目: 1
失败项目: 1
错误项目: 0

检查结果汇总
===========================================

主机: 测试主机 (192.168.1.100)
-------------------------------------------
✓ [PASS] 密码最小长度检查
   检查结果: 密码最小长度符合要求：8
   检查时间: 2025-08-20 03:57:28


规则详情
===========================================

规则ID: rule_001
规则名称: 密码最小长度检查
规则描述: 检查系统密码最小长度是否符合要求（≥8位）
安全类别: 身份鉴别
风险等级: 高

规则ID: rule_002
规则名称: root远程登录检查
规则描述: 检查是否禁止root用户远程登录
安全类别: 访问控制
风险等级: 高

规则ID: rule_003
规则名称: SSH密码登录检查
规则描述: 检查SSH是否禁用密码登录
安全类别: 访问控制
风险等级: 中

规则ID: rule_004
规则名称: 审计服务检查
规则描述: 检查auditd审计服务是否启用
安全类别: 安全审计
风险等级: 高

规则ID: rule_005
规则名称: /etc/passwd权限检查
规则描述: 检查/etc/passwd文件权限是否为644
安全类别: 访问控制
风险等级: 高

规则ID: rule_006
规则名称: /etc/shadow权限检查
规则描述: 检查/etc/shadow文件权限是否为600或640
安全类别: 访问控制
风险等级: 高

规则ID: rule_007
规则名称: SELinux状态检查
规则描述: 检查SELinux是否启用
安全类别: 访问控制
风险等级: 中

规则ID: rule_008
规则名称: 防火墙状态检查
规则描述: 检查防火墙是否启用
安全类别: 边界防护
风险等级: 高

规则ID: rule_009
规则名称: 自动更新检查
规则描述: 检查是否启用自动更新
安全类别: 恶意代码防范
风险等级: 中

规则ID: rule_010
规则名称: 日志目录权限检查
规则描述: 检查/var/log目录权限是否安全
安全类别: 安全审计
风险等级: 中

