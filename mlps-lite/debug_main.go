package main

import (
	"fmt"
	"log"
	"os"

	"github.com/lxn/walk"
	. "github.com/lxn/walk/declarative"
)

type AppMainWindow struct {
	*walk.MainWindow
	storage   *Storage
	checker   *Checker
	reporter  *ReportGenerator
	
	navTree   *walk.TreeView
	contentSV *walk.ScrollView
	
	currentPage string
}

func main() {
	// 添加调试输出
	fmt.Println("程序启动...")
	
	// 创建日志文件
	logFile, err := os.Create("debug.log")
	if err != nil {
		fmt.Printf("创建日志文件失败: %v\n", err)
	} else {
		defer logFile.Close()
		log.SetOutput(logFile)
	}
	
	log.Println("开始初始化...")
	fmt.Println("正在初始化存储...")
	
	storage := NewStorage()
	checker := NewChecker(storage, 5)
	reporter := NewReportGenerator(storage)
	
	fmt.Println("正在创建主窗口...")
	log.Println("创建主窗口...")

	mw := &AppMainWindow{
		storage:  storage,
		checker:  checker,
		reporter: reporter,
	}

	fmt.Println("正在初始化GUI...")
	log.Println("初始化GUI...")
	
	err = mw.initGUI()
	if err != nil {
		log.Printf("GUI初始化失败: %v", err)
		fmt.Printf("GUI初始化失败: %v\n", err)
		
		// 如果GUI失败，显示错误信息
		walk.MsgBox(nil, "启动错误", fmt.Sprintf("程序启动失败:\n%v\n\n请检查系统环境和依赖。", err), walk.MsgBoxIconError)
		return
	}

	fmt.Println("正在运行主循环...")
	log.Println("运行主循环...")
	
	mw.Run()
	
	fmt.Println("程序结束")
	log.Println("程序结束")
}

func (mw *AppMainWindow) initGUI() error {
	log.Println("开始创建主窗口...")
	
	err := MainWindow{
		AssignTo: &mw.MainWindow,
		Title:    "Linux等保基线核查工具 v1.0 (调试版)",
		MinSize:  Size{1000, 700},
		Layout:   HBox{MarginsZero: true},
		Children: []Widget{
			HSplitter{
				Children: []Widget{
					Composite{
						MaxSize: Size{250, 0},
						Layout:  VBox{MarginsZero: true},
						Children: []Widget{
							Label{
								Text: "功能导航",
								Font: Font{PointSize: 12, Bold: true},
							},
							TreeView{
								AssignTo: &mw.navTree,
								OnCurrentItemChanged: mw.onNavItemChanged,
							},
						},
					},
					ScrollView{
						AssignTo: &mw.contentSV,
						Layout:   VBox{},
						Children: []Widget{
							Label{
								Text: "欢迎使用Linux等保基线核查工具！\n\n请从左侧导航选择功能。",
								Font: Font{PointSize: 11},
							},
						},
					},
				},
			},
		},
		MenuItems: []MenuItem{
			Menu{
				Text: "文件(&F)",
				Items: []MenuItem{
					Action{
						Text:        "测试功能",
						OnTriggered: mw.testFeatures,
					},
					Separator{},
					Action{
						Text:        "退出",
						OnTriggered: func() { mw.Close() },
					},
				},
			},
			Menu{
				Text: "帮助(&H)",
				Items: []MenuItem{
					Action{
						Text:        "关于",
						OnTriggered: mw.showAbout,
					},
				},
			},
		},
		StatusBarItems: []StatusBarItem{
			{
				Text: "就绪 - 调试版本",
			},
		},
	}.Create()
	
	if err != nil {
		log.Printf("创建主窗口失败: %v", err)
		return err
	}
	
	log.Println("主窗口创建成功")
	return nil
}

func (mw *AppMainWindow) setupNavigation() error {
	log.Println("设置导航...")
	
	model := &TreeModel{}
	
	hostItem := &TreeItem{text: "主机管理", children: []*TreeItem{
		{text: "主机列表"},
		{text: "添加主机"},
	}}
	
	groupItem := &TreeItem{text: "主机分组", children: []*TreeItem{
		{text: "分组列表"},
		{text: "创建分组"},
	}}
	
	taskItem := &TreeItem{text: "核查任务", children: []*TreeItem{
		{text: "创建任务"},
		{text: "任务历史"},
	}}
	
	resultItem := &TreeItem{text: "核查结果", children: []*TreeItem{
		{text: "结果查看"},
		{text: "导出报告"},
	}}
	
	model.roots = []*TreeItem{hostItem, groupItem, taskItem, resultItem}
	
	err := mw.navTree.SetModel(model)
	if err != nil {
		log.Printf("设置导航模型失败: %v", err)
		return err
	}
	
	log.Println("导航设置成功")
	return nil
}

func (mw *AppMainWindow) onNavItemChanged() {
	log.Println("导航项改变...")
	
	if mw.navTree.CurrentItem() == nil {
		log.Println("当前项为空")
		return
	}
	
	item := mw.navTree.CurrentItem().(*TreeItem)
	log.Printf("选择项: %s", item.text)
	
	mw.switchPage(item.text)
}

func (mw *AppMainWindow) switchPage(pageName string) {
	log.Printf("切换页面: %s", pageName)
	
	if mw.currentPage == pageName {
		return
	}

	mw.currentPage = pageName
	
	// 简单的消息框响应
	message := fmt.Sprintf("您选择了: %s\n\n", pageName)
	
	switch pageName {
	case "主机列表":
		hosts, err := mw.storage.LoadHosts()
		if err != nil {
			message += fmt.Sprintf("加载主机失败: %v", err)
		} else {
			message += fmt.Sprintf("当前有 %d 台主机", len(hosts))
		}
	case "添加主机":
		message += "将打开添加主机对话框"
	default:
		message += "功能开发中..."
	}
	
	walk.MsgBox(mw.MainWindow, "页面切换", message, walk.MsgBoxIconInformation)
}

func (mw *AppMainWindow) testFeatures() {
	log.Println("测试功能...")
	
	// 测试存储
	testHost := Host{
		Name:     "测试主机",
		IP:       "*************",
		Port:     22,
		Username: "root",
		OS:       "Linux",
		Status:   "未连接",
	}
	
	err := mw.storage.AddHost(testHost)
	if err != nil {
		walk.MsgBox(mw.MainWindow, "测试结果", fmt.Sprintf("存储测试失败: %v", err), walk.MsgBoxIconError)
		return
	}
	
	hosts, err := mw.storage.LoadHosts()
	if err != nil {
		walk.MsgBox(mw.MainWindow, "测试结果", fmt.Sprintf("加载主机失败: %v", err), walk.MsgBoxIconError)
		return
	}
	
	message := fmt.Sprintf("功能测试成功！\n\n")
	message += fmt.Sprintf("主机数量: %d\n", len(hosts))
	message += fmt.Sprintf("内置规则: %d 条\n", len(BuiltinRules))
	message += fmt.Sprintf("程序运行正常")
	
	walk.MsgBox(mw.MainWindow, "测试结果", message, walk.MsgBoxIconInformation)
}

func (mw *AppMainWindow) showAbout() {
	walk.MsgBox(mw.MainWindow, "关于", 
		"Linux等保基线核查工具 v1.0 (调试版)\n\n"+
		"基于Go语言和Walk GUI框架开发\n"+
		"支持远程SSH连接进行安全基线检查\n\n"+
		"如果程序运行异常，请查看debug.log文件\n\n"+
		"开发者：Sec Team", 
		walk.MsgBoxIconInformation)
}

func (mw *AppMainWindow) Run() {
	log.Println("设置导航...")
	err := mw.setupNavigation()
	if err != nil {
		log.Printf("设置导航失败: %v", err)
		walk.MsgBox(mw.MainWindow, "错误", fmt.Sprintf("设置导航失败: %v", err), walk.MsgBoxIconError)
	}
	
	log.Println("显示主窗口...")
	mw.switchPage("主机列表")
	
	log.Println("进入消息循环...")
	mw.MainWindow.Run()
}

// TreeItem 和 TreeModel 定义
type TreeItem struct {
	text     string
	children []*TreeItem
}

func (ti *TreeItem) Text() string {
	return ti.text
}

func (ti *TreeItem) Parent() walk.TreeItem {
	return nil
}

func (ti *TreeItem) ChildCount() int {
	return len(ti.children)
}

func (ti *TreeItem) ChildAt(index int) walk.TreeItem {
	return ti.children[index]
}

type TreeModel struct {
	walk.TreeModelBase
	roots []*TreeItem
}

func (tm *TreeModel) LazyPopulation() bool {
	return false
}

func (tm *TreeModel) RootCount() int {
	return len(tm.roots)
}

func (tm *TreeModel) RootAt(index int) walk.TreeItem {
	return tm.roots[index]
}
