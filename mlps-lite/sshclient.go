package main

import (
	"fmt"
	"io/ioutil"
	"net"
	"strings"
	"time"

	"golang.org/x/crypto/ssh"
)

type SSHClient struct {
	client *ssh.Client
	host   *Host
}

func NewSSHClient(host *Host) *SSHClient {
	return &SSHClient{
		host: host,
	}
}

func (c *SSHClient) Connect() error {
	config := &ssh.ClientConfig{
		User:            c.host.Username,
		HostKeyCallback: ssh.InsecureIgnoreHostKey(),
		Timeout:         30 * time.Second,
	}

	if c.host.Password != "" {
		config.Auth = []ssh.AuthMethod{
			ssh.Password(c.host.Password),
		}
	} else if c.host.KeyPath != "" {
		key, err := ioutil.ReadFile(c.host.KeyPath)
		if err != nil {
			return fmt.Errorf("unable to read private key: %v", err)
		}

		signer, err := ssh.ParsePrivateKey(key)
		if err != nil {
			return fmt.Errorf("unable to parse private key: %v", err)
		}

		config.Auth = []ssh.AuthMethod{
			ssh.PublicKeys(signer),
		}
	} else {
		return fmt.Errorf("no authentication method provided")
	}

	addr := fmt.Sprintf("%s:%d", c.host.IP, c.host.Port)
	client, err := ssh.Dial("tcp", addr, config)
	if err != nil {
		return fmt.Errorf("failed to connect to %s: %v", addr, err)
	}

	c.client = client
	return nil
}

func (c *SSHClient) Close() error {
	if c.client != nil {
		return c.client.Close()
	}
	return nil
}

func (c *SSHClient) ExecuteCommand(command string) (string, error) {
	if c.client == nil {
		return "", fmt.Errorf("not connected")
	}

	session, err := c.client.NewSession()
	if err != nil {
		return "", fmt.Errorf("failed to create session: %v", err)
	}
	defer session.Close()

	output, err := session.CombinedOutput(command)
	if err != nil {
		return string(output), fmt.Errorf("command failed: %v", err)
	}

	return string(output), nil
}

func (c *SSHClient) ReadFile(filePath string) (string, error) {
	command := fmt.Sprintf("cat %s", filePath)
	return c.ExecuteCommand(command)
}

func (c *SSHClient) FileExists(filePath string) bool {
	command := fmt.Sprintf("test -f %s", filePath)
	_, err := c.ExecuteCommand(command)
	return err == nil
}

func (c *SSHClient) DirectoryExists(dirPath string) bool {
	command := fmt.Sprintf("test -d %s", dirPath)
	_, err := c.ExecuteCommand(command)
	return err == nil
}

func (c *SSHClient) GetFilePermissions(filePath string) (string, error) {
	command := fmt.Sprintf("stat -c '%%a' %s", filePath)
	output, err := c.ExecuteCommand(command)
	if err != nil {
		return "", err
	}
	return strings.TrimSpace(output), nil
}

func (c *SSHClient) CheckService(serviceName string) (bool, error) {
	command := fmt.Sprintf("systemctl is-active %s", serviceName)
	output, err := c.ExecuteCommand(command)
	if err != nil {
		return false, nil
	}
	return strings.TrimSpace(output) == "active", nil
}

func (c *SSHClient) CheckServiceEnabled(serviceName string) (bool, error) {
	command := fmt.Sprintf("systemctl is-enabled %s", serviceName)
	output, err := c.ExecuteCommand(command)
	if err != nil {
		return false, nil
	}
	status := strings.TrimSpace(output)
	return status == "enabled", nil
}

func (c *SSHClient) GetOSInfo() (string, error) {
	commands := []string{
		"cat /etc/os-release | grep PRETTY_NAME | cut -d'=' -f2 | tr -d '\"'",
		"cat /etc/redhat-release",
		"lsb_release -d | cut -f2",
		"uname -a",
	}

	for _, cmd := range commands {
		output, err := c.ExecuteCommand(cmd)
		if err == nil && strings.TrimSpace(output) != "" {
			return strings.TrimSpace(output), nil
		}
	}

	return "Unknown", nil
}

func (c *SSHClient) TestConnection() error {
	err := c.Connect()
	if err != nil {
		return err
	}
	defer c.Close()

	_, err = c.ExecuteCommand("echo 'test'")
	return err
}

func TestHostConnection(host *Host) error {
	timeout := 5 * time.Second
	conn, err := net.DialTimeout("tcp", fmt.Sprintf("%s:%d", host.IP, host.Port), timeout)
	if err != nil {
		return fmt.Errorf("network connection failed: %v", err)
	}
	conn.Close()

	client := NewSSHClient(host)
	return client.TestConnection()
}

func (c *SSHClient) GetConfigValue(configFile, key string) (string, error) {
	command := fmt.Sprintf("grep '^%s' %s | head -1 | awk '{print $2}'", key, configFile)
	output, err := c.ExecuteCommand(command)
	if err != nil {
		return "", err
	}
	return strings.TrimSpace(output), nil
}

func (c *SSHClient) GetConfigValueWithDefault(configFile, key, defaultValue string) string {
	value, err := c.GetConfigValue(configFile, key)
	if err != nil || value == "" {
		return defaultValue
	}
	return value
}

func (c *SSHClient) CheckProcessRunning(processName string) (bool, error) {
	command := fmt.Sprintf("pgrep %s", processName)
	_, err := c.ExecuteCommand(command)
	return err == nil, nil
}

func (c *SSHClient) GetFirewallStatus() (string, error) {
	commands := []string{
		"systemctl is-active firewalld",
		"systemctl is-active ufw",
		"iptables -L | head -1",
	}

	for _, cmd := range commands {
		output, err := c.ExecuteCommand(cmd)
		if err == nil && strings.TrimSpace(output) != "" {
			return strings.TrimSpace(output), nil
		}
	}

	return "unknown", nil
}

func (c *SSHClient) GetSELinuxStatus() (string, error) {
	command := "getenforce"
	output, err := c.ExecuteCommand(command)
	if err != nil {
		return "disabled", nil
	}
	return strings.TrimSpace(output), nil
}

func (c *SSHClient) CheckPackageInstalled(packageName string) (bool, error) {
	commands := []string{
		fmt.Sprintf("rpm -q %s", packageName),
		fmt.Sprintf("dpkg -l | grep %s", packageName),
		fmt.Sprintf("which %s", packageName),
	}

	for _, cmd := range commands {
		_, err := c.ExecuteCommand(cmd)
		if err == nil {
			return true, nil
		}
	}

	return false, nil
}

func (c *SSHClient) GetPasswordPolicy(policyType string) (string, error) {
	var command string
	switch policyType {
	case "min_length":
		command = "grep PASS_MIN_LEN /etc/login.defs | grep -v '^#' | awk '{print $2}'"
	case "max_days":
		command = "grep PASS_MAX_DAYS /etc/login.defs | grep -v '^#' | awk '{print $2}'"
	case "min_days":
		command = "grep PASS_MIN_DAYS /etc/login.defs | grep -v '^#' | awk '{print $2}'"
	case "warn_age":
		command = "grep PASS_WARN_AGE /etc/login.defs | grep -v '^#' | awk '{print $2}'"
	default:
		return "", fmt.Errorf("unknown policy type: %s", policyType)
	}

	output, err := c.ExecuteCommand(command)
	if err != nil {
		return "", err
	}
	return strings.TrimSpace(output), nil
}
