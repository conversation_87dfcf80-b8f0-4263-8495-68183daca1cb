package main

import (
	"bufio"
	"fmt"
	"os"
	"strconv"
	"strings"
)

func main() {
	fmt.Println("=== Linux等保基线核查工具 - 命令行版本 ===")
	fmt.Println("版本: v1.0")
	fmt.Println("运行环境测试...")
	
	storage := NewStorage()
	checker := NewChecker(storage, 2)
	reporter := NewReportGenerator(storage)
	
	scanner := bufio.NewScanner(os.Stdin)
	
	for {
		showMainMenu()
		fmt.Print("请选择功能 (输入数字): ")
		
		if !scanner.Scan() {
			break
		}
		
		choice := strings.TrimSpace(scanner.Text())
		
		switch choice {
		case "1":
			hostManagement(storage, scanner)
		case "2":
			groupManagement(storage, scanner)
		case "3":
			taskManagement(storage, checker, scanner)
		case "4":
			resultManagement(storage, scanner)
		case "5":
			reportManagement(storage, reporter, scanner)
		case "6":
			testCoreFeatures(storage, checker, reporter)
		case "0":
			fmt.Println("感谢使用，再见！")
			return
		default:
			fmt.Println("无效选择，请重新输入")
		}
		fmt.Println()
	}
}

func showMainMenu() {
	fmt.Println("\n=== 主菜单 ===")
	fmt.Println("1. 主机管理")
	fmt.Println("2. 主机分组")
	fmt.Println("3. 核查任务")
	fmt.Println("4. 核查结果")
	fmt.Println("5. 报告导出")
	fmt.Println("6. 功能测试")
	fmt.Println("0. 退出")
}

func hostManagement(storage *Storage, scanner *bufio.Scanner) {
	fmt.Println("\n=== 主机管理 ===")
	fmt.Println("1. 查看主机列表")
	fmt.Println("2. 添加主机")
	fmt.Println("3. 删除主机")
	fmt.Println("4. 测试连接")
	fmt.Print("请选择: ")
	
	if !scanner.Scan() {
		return
	}
	
	choice := strings.TrimSpace(scanner.Text())
	
	switch choice {
	case "1":
		listHosts(storage)
	case "2":
		addHost(storage, scanner)
	case "3":
		deleteHost(storage, scanner)
	case "4":
		testConnection(storage, scanner)
	default:
		fmt.Println("无效选择")
	}
}

func listHosts(storage *Storage) {
	hosts, err := storage.LoadHosts()
	if err != nil {
		fmt.Printf("加载主机失败: %v\n", err)
		return
	}
	
	if len(hosts) == 0 {
		fmt.Println("暂无主机")
		return
	}
	
	fmt.Printf("\n共有 %d 台主机:\n", len(hosts))
	fmt.Println("序号 | 主机名称 | IP地址 | 端口 | 用户名 | 状态")
	fmt.Println("-----|----------|--------|------|--------|------")
	
	for i, host := range hosts {
		fmt.Printf("%4d | %-8s | %-14s | %4d | %-6s | %s\n", 
			i+1, host.Name, host.IP, host.Port, host.Username, host.Status)
	}
}

func addHost(storage *Storage, scanner *bufio.Scanner) {
	fmt.Println("\n=== 添加主机 ===")
	
	fmt.Print("主机名称: ")
	scanner.Scan()
	name := strings.TrimSpace(scanner.Text())
	
	fmt.Print("IP地址: ")
	scanner.Scan()
	ip := strings.TrimSpace(scanner.Text())
	
	fmt.Print("SSH端口 (默认22): ")
	scanner.Scan()
	portStr := strings.TrimSpace(scanner.Text())
	port := 22
	if portStr != "" {
		if p, err := strconv.Atoi(portStr); err == nil {
			port = p
		}
	}
	
	fmt.Print("用户名: ")
	scanner.Scan()
	username := strings.TrimSpace(scanner.Text())
	
	fmt.Print("密码: ")
	scanner.Scan()
	password := strings.TrimSpace(scanner.Text())
	
	if name == "" || ip == "" || username == "" {
		fmt.Println("错误: 主机名称、IP地址和用户名不能为空")
		return
	}
	
	host := Host{
		Name:     name,
		IP:       ip,
		Port:     port,
		Username: username,
		Password: password,
		OS:       "Linux",
		Status:   "未连接",
	}
	
	err := storage.AddHost(host)
	if err != nil {
		fmt.Printf("添加主机失败: %v\n", err)
		return
	}
	
	fmt.Println("主机添加成功！")
}

func deleteHost(storage *Storage, scanner *bufio.Scanner) {
	listHosts(storage)
	
	hosts, err := storage.LoadHosts()
	if err != nil || len(hosts) == 0 {
		return
	}
	
	fmt.Print("请输入要删除的主机序号: ")
	scanner.Scan()
	indexStr := strings.TrimSpace(scanner.Text())
	
	index, err := strconv.Atoi(indexStr)
	if err != nil || index < 1 || index > len(hosts) {
		fmt.Println("无效的序号")
		return
	}
	
	host := hosts[index-1]
	fmt.Printf("确定要删除主机 %s (%s) 吗? (y/N): ", host.Name, host.IP)
	scanner.Scan()
	confirm := strings.ToLower(strings.TrimSpace(scanner.Text()))
	
	if confirm == "y" || confirm == "yes" {
		err := storage.DeleteHost(host.ID)
		if err != nil {
			fmt.Printf("删除失败: %v\n", err)
		} else {
			fmt.Println("删除成功！")
		}
	}
}

func testConnection(storage *Storage, scanner *bufio.Scanner) {
	listHosts(storage)
	
	hosts, err := storage.LoadHosts()
	if err != nil || len(hosts) == 0 {
		return
	}
	
	fmt.Print("请输入要测试的主机序号: ")
	scanner.Scan()
	indexStr := strings.TrimSpace(scanner.Text())
	
	index, err := strconv.Atoi(indexStr)
	if err != nil || index < 1 || index > len(hosts) {
		fmt.Println("无效的序号")
		return
	}
	
	host := hosts[index-1]
	fmt.Printf("正在测试主机 %s (%s) 的连接...\n", host.Name, host.IP)
	
	err = TestHostConnection(&host)
	if err != nil {
		fmt.Printf("连接失败: %v\n", err)
		host.Status = "连接失败"
	} else {
		fmt.Println("连接成功！")
		host.Status = "连接正常"
	}
	
	storage.AddHost(host)
}

func groupManagement(storage *Storage, scanner *bufio.Scanner) {
	fmt.Println("\n=== 主机分组 ===")
	fmt.Println("功能开发中...")
}

func taskManagement(storage *Storage, checker *Checker, scanner *bufio.Scanner) {
	fmt.Println("\n=== 核查任务 ===")
	fmt.Println("功能开发中...")
}

func resultManagement(storage *Storage, scanner *bufio.Scanner) {
	fmt.Println("\n=== 核查结果 ===")
	
	results, err := storage.LoadResults()
	if err != nil {
		fmt.Printf("加载结果失败: %v\n", err)
		return
	}
	
	if len(results) == 0 {
		fmt.Println("暂无检查结果")
		return
	}
	
	passCount := 0
	failCount := 0
	errorCount := 0
	
	for _, result := range results {
		switch result.Status {
		case "PASS":
			passCount++
		case "FAIL":
			failCount++
		case "ERROR":
			errorCount++
		}
	}
	
	fmt.Printf("检查结果统计:\n")
	fmt.Printf("总计: %d 项\n", len(results))
	fmt.Printf("通过: %d 项\n", passCount)
	fmt.Printf("失败: %d 项\n", failCount)
	fmt.Printf("错误: %d 项\n", errorCount)
}

func reportManagement(storage *Storage, reporter *ReportGenerator, scanner *bufio.Scanner) {
	fmt.Println("\n=== 报告导出 ===")
	
	executions, err := storage.LoadExecutions()
	if err != nil {
		fmt.Printf("加载执行记录失败: %v\n", err)
		return
	}
	
	if len(executions) == 0 {
		fmt.Println("暂无可导出的数据")
		return
	}
	
	fmt.Printf("找到 %d 条执行记录\n", len(executions))
	latestExecution := executions[len(executions)-1]
	
	fmt.Printf("正在生成最新执行记录的报告: %s\n", latestExecution.Name)
	
	txtFile, err := reporter.GenerateReport(latestExecution.ID, "txt", ".")
	if err != nil {
		fmt.Printf("生成报告失败: %v\n", err)
		return
	}
	
	fmt.Printf("报告生成成功: %s\n", txtFile)
}

func testCoreFeatures(storage *Storage, checker *Checker, reporter *ReportGenerator) {
	fmt.Println("\n=== 功能测试 ===")
	fmt.Println("正在测试核心功能...")
	
	// 运行之前的测试代码
	fmt.Println("1. 测试存储功能...")
	testHost := Host{
		ID:       "test-host-1",
		Name:     "测试主机",
		IP:       "*************",
		Port:     22,
		Username: "root",
		Password: "password",
		OS:       "CentOS 7",
		Status:   "未连接",
	}
	
	err := storage.AddHost(testHost)
	if err != nil {
		fmt.Printf("✗ 添加主机失败: %v\n", err)
	} else {
		fmt.Printf("✓ 成功添加测试主机\n")
	}
	
	fmt.Println("2. 测试规则定义...")
	fmt.Printf("✓ 内置规则总数: %d\n", len(BuiltinRules))
	
	fmt.Println("3. 测试完成！")
}
