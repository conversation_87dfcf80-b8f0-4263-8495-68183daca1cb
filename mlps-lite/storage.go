package main

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
	"time"
)

type Host struct {
	ID       string `json:"id"`
	Name     string `json:"name"`
	IP       string `json:"ip"`
	Port     int    `json:"port"`
	Username string `json:"username"`
	Password string `json:"password,omitempty"`
	KeyPath  string `json:"key_path,omitempty"`
	OS       string `json:"os"`
	Status   string `json:"status"`
	GroupID  string `json:"group_id,omitempty"`
}

type Group struct {
	ID          string   `json:"id"`
	Name        string   `json:"name"`
	Description string   `json:"description"`
	HostIDs     []string `json:"host_ids"`
	CreatedAt   string   `json:"created_at"`
}

type CheckResult struct {
	ID          string `json:"id"`
	HostID      string `json:"host_id"`
	HostIP      string `json:"host_ip"`
	RuleID      string `json:"rule_id"`
	RuleName    string `json:"rule_name"`
	Status      string `json:"status"`
	Message     string `json:"message"`
	Suggestion  string `json:"suggestion"`
	CheckedAt   string `json:"checked_at"`
	ExecutionID string `json:"execution_id"`
}

type TaskExecution struct {
	ID          string   `json:"id"`
	Name        string   `json:"name"`
	HostIDs     []string `json:"host_ids"`
	RuleIDs     []string `json:"rule_ids"`
	Status      string   `json:"status"`
	StartTime   string   `json:"start_time"`
	EndTime     string   `json:"end_time"`
	TotalHosts  int      `json:"total_hosts"`
	TotalRules  int      `json:"total_rules"`
	PassCount   int      `json:"pass_count"`
	FailCount   int      `json:"fail_count"`
	ErrorCount  int      `json:"error_count"`
}

type Storage struct {
	hostsFile      string
	groupsFile     string
	resultsFile    string
	executionsFile string
}

func NewStorage() *Storage {
	return &Storage{
		hostsFile:      "hosts.json",
		groupsFile:     "groups.json",
		resultsFile:    "results.json",
		executionsFile: "executions.json",
	}
}

func (s *Storage) LoadHosts() ([]Host, error) {
	var hosts []Host
	data, err := ioutil.ReadFile(s.hostsFile)
	if err != nil {
		if os.IsNotExist(err) {
			return hosts, nil
		}
		return nil, err
	}
	err = json.Unmarshal(data, &hosts)
	return hosts, err
}

func (s *Storage) SaveHosts(hosts []Host) error {
	data, err := json.MarshalIndent(hosts, "", "  ")
	if err != nil {
		return err
	}
	return ioutil.WriteFile(s.hostsFile, data, 0644)
}

func (s *Storage) LoadGroups() ([]Group, error) {
	var groups []Group
	data, err := ioutil.ReadFile(s.groupsFile)
	if err != nil {
		if os.IsNotExist(err) {
			return groups, nil
		}
		return nil, err
	}
	err = json.Unmarshal(data, &groups)
	return groups, err
}

func (s *Storage) SaveGroups(groups []Group) error {
	data, err := json.MarshalIndent(groups, "", "  ")
	if err != nil {
		return err
	}
	return ioutil.WriteFile(s.groupsFile, data, 0644)
}

func (s *Storage) LoadResults() ([]CheckResult, error) {
	var results []CheckResult
	data, err := ioutil.ReadFile(s.resultsFile)
	if err != nil {
		if os.IsNotExist(err) {
			return results, nil
		}
		return nil, err
	}
	err = json.Unmarshal(data, &results)
	return results, err
}

func (s *Storage) SaveResults(results []CheckResult) error {
	data, err := json.MarshalIndent(results, "", "  ")
	if err != nil {
		return err
	}
	return ioutil.WriteFile(s.resultsFile, data, 0644)
}

func (s *Storage) LoadExecutions() ([]TaskExecution, error) {
	var executions []TaskExecution
	data, err := ioutil.ReadFile(s.executionsFile)
	if err != nil {
		if os.IsNotExist(err) {
			return executions, nil
		}
		return nil, err
	}
	err = json.Unmarshal(data, &executions)
	return executions, err
}

func (s *Storage) SaveExecutions(executions []TaskExecution) error {
	data, err := json.MarshalIndent(executions, "", "  ")
	if err != nil {
		return err
	}
	return ioutil.WriteFile(s.executionsFile, data, 0644)
}

func (s *Storage) AddHost(host Host) error {
	hosts, err := s.LoadHosts()
	if err != nil {
		return err
	}
	
	if host.ID == "" {
		host.ID = generateID()
	}
	
	for i, h := range hosts {
		if h.ID == host.ID {
			hosts[i] = host
			return s.SaveHosts(hosts)
		}
	}
	
	hosts = append(hosts, host)
	return s.SaveHosts(hosts)
}

func (s *Storage) DeleteHost(hostID string) error {
	hosts, err := s.LoadHosts()
	if err != nil {
		return err
	}
	
	for i, host := range hosts {
		if host.ID == hostID {
			hosts = append(hosts[:i], hosts[i+1:]...)
			return s.SaveHosts(hosts)
		}
	}
	
	return fmt.Errorf("host not found: %s", hostID)
}

func (s *Storage) GetHost(hostID string) (*Host, error) {
	hosts, err := s.LoadHosts()
	if err != nil {
		return nil, err
	}
	
	for _, host := range hosts {
		if host.ID == hostID {
			return &host, nil
		}
	}
	
	return nil, fmt.Errorf("host not found: %s", hostID)
}

func (s *Storage) AddGroup(group Group) error {
	groups, err := s.LoadGroups()
	if err != nil {
		return err
	}
	
	if group.ID == "" {
		group.ID = generateID()
	}
	
	if group.CreatedAt == "" {
		group.CreatedAt = time.Now().Format("2006-01-02 15:04:05")
	}
	
	for i, g := range groups {
		if g.ID == group.ID {
			groups[i] = group
			return s.SaveGroups(groups)
		}
	}
	
	groups = append(groups, group)
	return s.SaveGroups(groups)
}

func (s *Storage) DeleteGroup(groupID string) error {
	groups, err := s.LoadGroups()
	if err != nil {
		return err
	}
	
	for i, group := range groups {
		if group.ID == groupID {
			groups = append(groups[:i], groups[i+1:]...)
			return s.SaveGroups(groups)
		}
	}
	
	return fmt.Errorf("group not found: %s", groupID)
}

func (s *Storage) AddResult(result CheckResult) error {
	results, err := s.LoadResults()
	if err != nil {
		return err
	}
	
	if result.ID == "" {
		result.ID = generateID()
	}
	
	if result.CheckedAt == "" {
		result.CheckedAt = time.Now().Format("2006-01-02 15:04:05")
	}
	
	results = append(results, result)
	return s.SaveResults(results)
}

func (s *Storage) AddExecution(execution TaskExecution) error {
	executions, err := s.LoadExecutions()
	if err != nil {
		return err
	}
	
	if execution.ID == "" {
		execution.ID = generateID()
	}
	
	if execution.StartTime == "" {
		execution.StartTime = time.Now().Format("2006-01-02 15:04:05")
	}
	
	for i, e := range executions {
		if e.ID == execution.ID {
			executions[i] = execution
			return s.SaveExecutions(executions)
		}
	}
	
	executions = append(executions, execution)
	return s.SaveExecutions(executions)
}

func generateID() string {
	return fmt.Sprintf("%d", time.Now().UnixNano())
}
