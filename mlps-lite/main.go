package main

import (
	"bufio"
	"fmt"
	"log"
	"os"
	"strconv"
	"strings"
)

type App struct {
	storage  *Storage
	checker  *Checker
	reporter *ReportGenerator
	scanner  *bufio.Scanner
}

func main() {
	fmt.Println("Linux等保基线核查工具 v1.0 (命令行版本)")
	fmt.Println("========================================")

	storage := NewStorage()
	checker := NewChecker(storage, 5)
	reporter := NewReportGenerator(storage)

	app := &App{
		storage:  storage,
		checker:  checker,
		reporter: reporter,
		scanner:  bufio.NewScanner(os.Stdin),
	}

	app.run()
}

func (mw *MainWindow) initGUI() error {
	return MainWindow{
		AssignTo: &mw.MainWindow,
		Title:    "Linux等保基线核查工具 v1.0",
		MinSize:  Size{1200, 800},
		Layout:   HBox{MarginsZero: true},
		Children: []Widget{
			HSplitter{
				Children: []Widget{
					Composite{
						MaxSize: Size{250, 0},
						Layout:  VBox{MarginsZero: true},
						Children: []Widget{
							Label{
								Text: "功能导航",
								Font: Font{PointSize: 12, Bold: true},
							},
							TreeView{
								AssignTo: &mw.navTree,
								OnCurrentItemChanged: mw.onNavItemChanged,
							},
						},
					},
					ScrollView{
						AssignTo: &mw.contentSV,
						Layout:   VBox{},
					},
				},
			},
		},
		MenuItems: []MenuItem{
			Menu{
				Text: "文件(&F)",
				Items: []MenuItem{
					Action{
						Text:        "导入主机配置...",
						OnTriggered: mw.importHosts,
					},
					Action{
						Text:        "导出主机配置...",
						OnTriggered: mw.exportHosts,
					},
					Separator{},
					Action{
						Text:        "退出",
						OnTriggered: func() { mw.Close() },
					},
				},
			},
			Menu{
				Text: "工具(&T)",
				Items: []MenuItem{
					Action{
						Text:        "批量连通性测试",
						OnTriggered: mw.batchConnectivityTest,
					},
					Action{
						Text:        "清理历史数据",
						OnTriggered: mw.cleanHistoryData,
					},
				},
			},
			Menu{
				Text: "帮助(&H)",
				Items: []MenuItem{
					Action{
						Text:        "关于",
						OnTriggered: mw.showAbout,
					},
				},
			},
		},
		StatusBarItems: []StatusBarItem{
			{
				Text: "就绪",
			},
		},
	}.Create()
}

func (mw *MainWindow) setupNavigation() error {
	model := walk.NewTreeModel()
	
	hostItem := &TreeItem{text: "主机管理", children: []*TreeItem{
		{text: "主机列表"},
		{text: "添加主机"},
	}}
	
	groupItem := &TreeItem{text: "主机分组", children: []*TreeItem{
		{text: "分组列表"},
		{text: "创建分组"},
	}}
	
	taskItem := &TreeItem{text: "核查任务", children: []*TreeItem{
		{text: "创建任务"},
		{text: "任务历史"},
	}}
	
	resultItem := &TreeItem{text: "核查结果", children: []*TreeItem{
		{text: "结果查看"},
		{text: "导出报告"},
	}}
	
	model.roots = []*TreeItem{hostItem, groupItem, taskItem, resultItem}
	
	return mw.navTree.SetModel(model)
}

func (mw *MainWindow) onNavItemChanged() {
	if mw.navTree.CurrentItem() == nil {
		return
	}
	
	item := mw.navTree.CurrentItem().(*TreeItem)
	mw.switchPage(item.text)
}

func (mw *MainWindow) switchPage(pageName string) {
	if mw.currentPage == pageName {
		return
	}
	
	mw.currentPage = pageName
	mw.contentSV.Children().Clear()
	
	var content Widget
	
	switch pageName {
	case "主机列表":
		content = mw.createHostListPage()
	case "添加主机":
		content = mw.createAddHostPage()
	case "分组列表":
		content = mw.createGroupListPage()
	case "创建分组":
		content = mw.createAddGroupPage()
	case "创建任务":
		content = mw.createTaskPage()
	case "任务历史":
		content = mw.createTaskHistoryPage()
	case "结果查看":
		content = mw.createResultPage()
	case "导出报告":
		content = mw.createReportPage()
	default:
		content = Label{Text: "页面开发中..."}
	}
	
	mw.contentSV.Children().Add(content)
}

func (mw *MainWindow) importHosts() {
	walk.MsgBox(mw, "导入主机", "功能开发中...", walk.MsgBoxIconInformation)
}

func (mw *MainWindow) exportHosts() {
	walk.MsgBox(mw, "导出主机", "功能开发中...", walk.MsgBoxIconInformation)
}

func (mw *MainWindow) batchConnectivityTest() {
	walk.MsgBox(mw, "批量测试", "功能开发中...", walk.MsgBoxIconInformation)
}

func (mw *MainWindow) cleanHistoryData() {
	result := walk.MsgBox(mw, "清理数据", "确定要清理历史数据吗？此操作不可恢复。", walk.MsgBoxYesNo|walk.MsgBoxIconQuestion)
	if result == walk.DlgCmdYes {
		walk.MsgBox(mw, "清理数据", "功能开发中...", walk.MsgBoxIconInformation)
	}
}

func (mw *MainWindow) showAbout() {
	walk.MsgBox(mw, "关于", 
		"Linux等保基线核查工具 v1.0\n\n"+
		"基于Go语言和Walk GUI框架开发\n"+
		"支持远程SSH连接进行安全基线检查\n\n"+
		"开发者：Sec Team", 
		walk.MsgBoxIconInformation)
}

type TreeItem struct {
	text     string
	children []*TreeItem
}

func (ti *TreeItem) Text() string {
	return ti.text
}

func (ti *TreeItem) Parent() walk.TreeItem {
	return nil
}

func (ti *TreeItem) ChildCount() int {
	return len(ti.children)
}

func (ti *TreeItem) ChildAt(index int) walk.TreeItem {
	return ti.children[index]
}

type TreeModel struct {
	walk.TreeModelBase
	roots []*TreeItem
}

func (tm *TreeModel) LazyPopulation() bool {
	return false
}

func (tm *TreeModel) RootCount() int {
	return len(tm.roots)
}

func (tm *TreeModel) RootAt(index int) walk.TreeItem {
	return tm.roots[index]
}

func (mw *MainWindow) Run() {
	err := mw.setupNavigation()
	if err != nil {
		log.Fatal(err)
	}
	
	mw.switchPage("主机列表")
	
	mw.MainWindow.Run()
}

func (mw *MainWindow) createHostListPage() Widget {
	return Composite{
		Layout: VBox{},
		Children: []Widget{
			Label{
				Text: "主机管理 - 主机列表",
				Font: Font{PointSize: 14, Bold: true},
			},
			Label{
				Text: "页面开发中...",
			},
		},
	}
}

func (mw *MainWindow) createAddHostPage() Widget {
	return Composite{
		Layout: VBox{},
		Children: []Widget{
			Label{
				Text: "主机管理 - 添加主机",
				Font: Font{PointSize: 14, Bold: true},
			},
			Label{
				Text: "页面开发中...",
			},
		},
	}
}

func (mw *MainWindow) createGroupListPage() Widget {
	return Composite{
		Layout: VBox{},
		Children: []Widget{
			Label{
				Text: "主机分组 - 分组列表",
				Font: Font{PointSize: 14, Bold: true},
			},
			Label{
				Text: "页面开发中...",
			},
		},
	}
}

func (mw *MainWindow) createAddGroupPage() Widget {
	return Composite{
		Layout: VBox{},
		Children: []Widget{
			Label{
				Text: "主机分组 - 创建分组",
				Font: Font{PointSize: 14, Bold: true},
			},
			Label{
				Text: "页面开发中...",
			},
		},
	}
}

func (mw *MainWindow) createTaskPage() Widget {
	return Composite{
		Layout: VBox{},
		Children: []Widget{
			Label{
				Text: "核查任务 - 创建任务",
				Font: Font{PointSize: 14, Bold: true},
			},
			Label{
				Text: "页面开发中...",
			},
		},
	}
}

func (mw *MainWindow) createTaskHistoryPage() Widget {
	return Composite{
		Layout: VBox{},
		Children: []Widget{
			Label{
				Text: "核查任务 - 任务历史",
				Font: Font{PointSize: 14, Bold: true},
			},
			Label{
				Text: "页面开发中...",
			},
		},
	}
}

func (mw *MainWindow) createResultPage() Widget {
	return Composite{
		Layout: VBox{},
		Children: []Widget{
			Label{
				Text: "核查结果 - 结果查看",
				Font: Font{PointSize: 14, Bold: true},
			},
			Label{
				Text: "页面开发中...",
			},
		},
	}
}

func (mw *MainWindow) createReportPage() Widget {
	return Composite{
		Layout: VBox{},
		Children: []Widget{
			Label{
				Text: "核查结果 - 导出报告",
				Font: Font{PointSize: 14, Bold: true},
			},
			Label{
				Text: "页面开发中...",
			},
		},
	}
}
