package main

import (
	"log"

	"github.com/lxn/walk"
	. "github.com/lxn/walk/declarative"
)

type AppMainWindow struct {
	*walk.MainWindow
	storage   *Storage
	checker   *Checker
	reporter  *ReportGenerator

	navTree   *walk.TreeView
	contentSV *walk.ScrollView

	currentPage string
}

func main() {
	storage := NewStorage()
	checker := NewChecker(storage, 5)
	reporter := NewReportGenerator(storage)

	mw := &AppMainWindow{
		storage:  storage,
		checker:  checker,
		reporter: reporter,
	}

	err := mw.initGUI()
	if err != nil {
		log.Fatal(err)
	}

	mw.Run()
}

func (mw *AppMainWindow) initGUI() error {
	return MainWindow{
		AssignTo: &mw.MainWindow,
		Title:    "Linux等保基线核查工具 v1.0",
		MinSize:  Size{1200, 800},
		Layout:   HBox{MarginsZero: true},
		Children: []Widget{
			HSplitter{
				Children: []Widget{
					Composite{
						MaxSize: Size{250, 0},
						Layout:  VBox{MarginsZero: true},
						Children: []Widget{
							Label{
								Text: "功能导航",
								Font: Font{PointSize: 12, Bold: true},
							},
							TreeView{
								AssignTo: &mw.navTree,
								OnCurrentItemChanged: mw.onNavItemChanged,
							},
						},
					},
					ScrollView{
						AssignTo: &mw.contentSV,
						Layout:   VBox{},
					},
				},
			},
		},
		MenuItems: []MenuItem{
			Menu{
				Text: "文件(&F)",
				Items: []MenuItem{
					Action{
						Text:        "导入主机配置...",
						OnTriggered: mw.importHosts,
					},
					Action{
						Text:        "导出主机配置...",
						OnTriggered: mw.exportHosts,
					},
					Separator{},
					Action{
						Text:        "退出",
						OnTriggered: func() { mw.Close() },
					},
				},
			},
			Menu{
				Text: "工具(&T)",
				Items: []MenuItem{
					Action{
						Text:        "批量连通性测试",
						OnTriggered: mw.batchConnectivityTest,
					},
					Action{
						Text:        "清理历史数据",
						OnTriggered: mw.cleanHistoryData,
					},
				},
			},
			Menu{
				Text: "帮助(&H)",
				Items: []MenuItem{
					Action{
						Text:        "关于",
						OnTriggered: mw.showAbout,
					},
				},
			},
		},
		StatusBarItems: []StatusBarItem{
			{
				Text: "就绪",
			},
		},
	}.Create()
}

func (mw *AppMainWindow) setupNavigation() error {
	model := &TreeModel{}

	hostItem := &TreeItem{text: "主机管理", children: []*TreeItem{
		{text: "主机列表"},
		{text: "添加主机"},
	}}

	groupItem := &TreeItem{text: "主机分组", children: []*TreeItem{
		{text: "分组列表"},
		{text: "创建分组"},
	}}

	taskItem := &TreeItem{text: "核查任务", children: []*TreeItem{
		{text: "创建任务"},
		{text: "任务历史"},
	}}

	resultItem := &TreeItem{text: "核查结果", children: []*TreeItem{
		{text: "结果查看"},
		{text: "导出报告"},
	}}

	model.roots = []*TreeItem{hostItem, groupItem, taskItem, resultItem}

	return mw.navTree.SetModel(model)
}

func (mw *AppMainWindow) onNavItemChanged() {
	if mw.navTree.CurrentItem() == nil {
		return
	}

	item := mw.navTree.CurrentItem().(*TreeItem)
	mw.switchPage(item.text)
}

func (mw *AppMainWindow) switchPage(pageName string) {
	if mw.currentPage == pageName {
		return
	}

	mw.currentPage = pageName
	// TODO: 实现页面切换逻辑
	// 当前版本暂时不实现动态内容切换
}

func (mw *AppMainWindow) importHosts() {
	walk.MsgBox(mw.MainWindow, "导入主机", "功能开发中...", walk.MsgBoxIconInformation)
}

func (mw *AppMainWindow) exportHosts() {
	walk.MsgBox(mw.MainWindow, "导出主机", "功能开发中...", walk.MsgBoxIconInformation)
}

func (mw *AppMainWindow) batchConnectivityTest() {
	walk.MsgBox(mw.MainWindow, "批量测试", "功能开发中...", walk.MsgBoxIconInformation)
}

func (mw *AppMainWindow) cleanHistoryData() {
	result := walk.MsgBox(mw.MainWindow, "清理数据", "确定要清理历史数据吗？此操作不可恢复。", walk.MsgBoxYesNo|walk.MsgBoxIconQuestion)
	if result == walk.DlgCmdYes {
		walk.MsgBox(mw.MainWindow, "清理数据", "功能开发中...", walk.MsgBoxIconInformation)
	}
}

func (mw *AppMainWindow) showAbout() {
	walk.MsgBox(mw.MainWindow, "关于",
		"Linux等保基线核查工具 v1.0\n\n"+
		"基于Go语言和Walk GUI框架开发\n"+
		"支持远程SSH连接进行安全基线检查\n\n"+
		"开发者：Sec Team",
		walk.MsgBoxIconInformation)
}

type TreeItem struct {
	text     string
	children []*TreeItem
}

func (ti *TreeItem) Text() string {
	return ti.text
}

func (ti *TreeItem) Parent() walk.TreeItem {
	return nil
}

func (ti *TreeItem) ChildCount() int {
	return len(ti.children)
}

func (ti *TreeItem) ChildAt(index int) walk.TreeItem {
	return ti.children[index]
}

type TreeModel struct {
	walk.TreeModelBase
	roots []*TreeItem
}

func (tm *TreeModel) LazyPopulation() bool {
	return false
}

func (tm *TreeModel) RootCount() int {
	return len(tm.roots)
}

func (tm *TreeModel) RootAt(index int) walk.TreeItem {
	return tm.roots[index]
}

func (mw *AppMainWindow) Run() {
	err := mw.setupNavigation()
	if err != nil {
		log.Fatal(err)
	}

	mw.switchPage("主机列表")

	mw.MainWindow.Run()
}

func (mw *AppMainWindow) createHostListPage() Widget {
	return Composite{
		Layout: VBox{},
		Children: []Widget{
			Label{
				Text: "主机管理 - 主机列表",
				Font: Font{PointSize: 14, Bold: true},
			},
			Label{
				Text: "页面开发中...",
			},
		},
	}
}

func (mw *AppMainWindow) createAddHostPage() Widget {
	return Composite{
		Layout: VBox{},
		Children: []Widget{
			Label{
				Text: "主机管理 - 添加主机",
				Font: Font{PointSize: 14, Bold: true},
			},
			Label{
				Text: "页面开发中...",
			},
		},
	}
}

func (mw *AppMainWindow) createGroupListPage() Widget {
	return Composite{
		Layout: VBox{},
		Children: []Widget{
			Label{
				Text: "主机分组 - 分组列表",
				Font: Font{PointSize: 14, Bold: true},
			},
			Label{
				Text: "页面开发中...",
			},
		},
	}
}

func (mw *AppMainWindow) createAddGroupPage() Widget {
	return Composite{
		Layout: VBox{},
		Children: []Widget{
			Label{
				Text: "主机分组 - 创建分组",
				Font: Font{PointSize: 14, Bold: true},
			},
			Label{
				Text: "页面开发中...",
			},
		},
	}
}

func (mw *AppMainWindow) createTaskPage() Widget {
	return Composite{
		Layout: VBox{},
		Children: []Widget{
			Label{
				Text: "核查任务 - 创建任务",
				Font: Font{PointSize: 14, Bold: true},
			},
			Label{
				Text: "页面开发中...",
			},
		},
	}
}

func (mw *AppMainWindow) createTaskHistoryPage() Widget {
	return Composite{
		Layout: VBox{},
		Children: []Widget{
			Label{
				Text: "核查任务 - 任务历史",
				Font: Font{PointSize: 14, Bold: true},
			},
			Label{
				Text: "页面开发中...",
			},
		},
	}
}

func (mw *AppMainWindow) createResultPage() Widget {
	return Composite{
		Layout: VBox{},
		Children: []Widget{
			Label{
				Text: "核查结果 - 结果查看",
				Font: Font{PointSize: 14, Bold: true},
			},
			Label{
				Text: "页面开发中...",
			},
		},
	}
}

func (mw *AppMainWindow) createReportPage() Widget {
	return Composite{
		Layout: VBox{},
		Children: []Widget{
			Label{
				Text: "核查结果 - 导出报告",
				Font: Font{PointSize: 14, Bold: true},
			},
			Label{
				Text: "页面开发中...",
			},
		},
	}
}
