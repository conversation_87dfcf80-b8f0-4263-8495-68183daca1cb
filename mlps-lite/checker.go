package main

import (
	"fmt"
	"sync"
	"time"
)

type CheckTask struct {
	Host    *Host
	Rules   []Rule
	Results chan CheckResult
	Errors  chan error
}

type Checker struct {
	storage     *Storage
	concurrency int
}

func New<PERSON>hecker(storage *Storage, concurrency int) *Checker {
	return &Checker{
		storage:     storage,
		concurrency: concurrency,
	}
}

func (c *Checker) CheckHost(host *Host, rules []Rule, executionID string) ([]CheckResult, error) {
	client := NewSSHClient(host)
	err := client.Connect()
	if err != nil {
		return nil, fmt.Errorf("连接主机失败: %v", err)
	}
	defer client.Close()

	var results []CheckResult
	for _, rule := range rules {
		if !rule.Enabled {
			continue
		}

		result := CheckResult{
			HostID:      host.ID,
			HostIP:      host.IP,
			RuleID:      rule.ID,
			RuleName:    rule.Name,
			ExecutionID: executionID,
			CheckedAt:   time.Now().Format("2006-01-02 15:04:05"),
		}

		checker := GetRuleChecker(rule.ID)
		if checker == nil {
			result.Status = "ERROR"
			result.Message = "规则检查器未找到"
			result.Suggestion = "联系管理员检查规则配置"
		} else {
			passed, message, suggestion, err := checker.Check(client)
			if err != nil {
				result.Status = "ERROR"
				result.Message = fmt.Sprintf("检查失败: %v", err)
				result.Suggestion = suggestion
			} else if passed {
				result.Status = "PASS"
				result.Message = message
				result.Suggestion = ""
			} else {
				result.Status = "FAIL"
				result.Message = message
				result.Suggestion = suggestion
			}
		}

		results = append(results, result)
	}

	return results, nil
}

func (c *Checker) CheckHosts(hosts []*Host, rules []Rule, executionID string) ([]CheckResult, error) {
	if len(hosts) == 0 {
		return nil, fmt.Errorf("没有指定主机")
	}

	if len(rules) == 0 {
		return nil, fmt.Errorf("没有指定规则")
	}

	var allResults []CheckResult
	var mu sync.Mutex
	var wg sync.WaitGroup

	semaphore := make(chan struct{}, c.concurrency)
	errorChan := make(chan error, len(hosts))

	for _, host := range hosts {
		wg.Add(1)
		go func(h *Host) {
			defer wg.Done()
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			results, err := c.CheckHost(h, rules, executionID)
			if err != nil {
				errorChan <- fmt.Errorf("主机 %s (%s) 检查失败: %v", h.Name, h.IP, err)
				
				for _, rule := range rules {
					if !rule.Enabled {
						continue
					}
					errorResult := CheckResult{
						HostID:      h.ID,
						HostIP:      h.IP,
						RuleID:      rule.ID,
						RuleName:    rule.Name,
						Status:      "ERROR",
						Message:     fmt.Sprintf("连接失败: %v", err),
						Suggestion:  "检查网络连接和SSH配置",
						ExecutionID: executionID,
						CheckedAt:   time.Now().Format("2006-01-02 15:04:05"),
					}
					
					mu.Lock()
					allResults = append(allResults, errorResult)
					mu.Unlock()
				}
				return
			}

			mu.Lock()
			allResults = append(allResults, results...)
			mu.Unlock()
		}(host)
	}

	wg.Wait()
	close(errorChan)

	var errors []error
	for err := range errorChan {
		errors = append(errors, err)
	}

	if len(errors) > 0 && len(allResults) == 0 {
		return nil, fmt.Errorf("所有主机检查都失败了")
	}

	return allResults, nil
}

func (c *Checker) ExecuteTask(hostIDs []string, ruleIDs []string, taskName string) (*TaskExecution, error) {
	execution := TaskExecution{
		ID:         generateID(),
		Name:       taskName,
		HostIDs:    hostIDs,
		RuleIDs:    ruleIDs,
		Status:     "RUNNING",
		StartTime:  time.Now().Format("2006-01-02 15:04:05"),
		TotalHosts: len(hostIDs),
		TotalRules: len(ruleIDs),
	}

	err := c.storage.AddExecution(execution)
	if err != nil {
		return nil, fmt.Errorf("保存执行记录失败: %v", err)
	}

	var hosts []*Host
	for _, hostID := range hostIDs {
		host, err := c.storage.GetHost(hostID)
		if err != nil {
			execution.Status = "ERROR"
			execution.EndTime = time.Now().Format("2006-01-02 15:04:05")
			c.storage.AddExecution(execution)
			return nil, fmt.Errorf("获取主机信息失败: %v", err)
		}
		hosts = append(hosts, host)
	}

	var rules []Rule
	for _, ruleID := range ruleIDs {
		for _, rule := range BuiltinRules {
			if rule.ID == ruleID && rule.Enabled {
				rules = append(rules, rule)
				break
			}
		}
	}

	if len(rules) == 0 {
		execution.Status = "ERROR"
		execution.EndTime = time.Now().Format("2006-01-02 15:04:05")
		c.storage.AddExecution(execution)
		return nil, fmt.Errorf("没有找到有效的规则")
	}

	results, err := c.CheckHosts(hosts, rules, execution.ID)
	if err != nil {
		execution.Status = "ERROR"
		execution.EndTime = time.Now().Format("2006-01-02 15:04:05")
		c.storage.AddExecution(execution)
		return nil, fmt.Errorf("执行检查失败: %v", err)
	}

	for _, result := range results {
		err := c.storage.AddResult(result)
		if err != nil {
			fmt.Printf("保存检查结果失败: %v\n", err)
		}

		switch result.Status {
		case "PASS":
			execution.PassCount++
		case "FAIL":
			execution.FailCount++
		case "ERROR":
			execution.ErrorCount++
		}
	}

	execution.Status = "COMPLETED"
	execution.EndTime = time.Now().Format("2006-01-02 15:04:05")
	err = c.storage.AddExecution(execution)
	if err != nil {
		fmt.Printf("更新执行记录失败: %v\n", err)
	}

	return &execution, nil
}

func (c *Checker) TestHostConnections(hosts []*Host) map[string]error {
	results := make(map[string]error)
	var mu sync.Mutex
	var wg sync.WaitGroup

	semaphore := make(chan struct{}, c.concurrency)

	for _, host := range hosts {
		wg.Add(1)
		go func(h *Host) {
			defer wg.Done()
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			err := TestHostConnection(h)
			
			mu.Lock()
			results[h.ID] = err
			mu.Unlock()
		}(host)
	}

	wg.Wait()
	return results
}

func (c *Checker) GetExecutionResults(executionID string) ([]CheckResult, error) {
	allResults, err := c.storage.LoadResults()
	if err != nil {
		return nil, err
	}

	var results []CheckResult
	for _, result := range allResults {
		if result.ExecutionID == executionID {
			results = append(results, result)
		}
	}

	return results, nil
}

func (c *Checker) GetHostResults(hostID string) ([]CheckResult, error) {
	allResults, err := c.storage.LoadResults()
	if err != nil {
		return nil, err
	}

	var results []CheckResult
	for _, result := range allResults {
		if result.HostID == hostID {
			results = append(results, result)
		}
	}

	return results, nil
}

func (c *Checker) GetRuleResults(ruleID string) ([]CheckResult, error) {
	allResults, err := c.storage.LoadResults()
	if err != nil {
		return nil, err
	}

	var results []CheckResult
	for _, result := range allResults {
		if result.RuleID == ruleID {
			results = append(results, result)
		}
	}

	return results, nil
}

func (c *Checker) GetResultsByStatus(status string) ([]CheckResult, error) {
	allResults, err := c.storage.LoadResults()
	if err != nil {
		return nil, err
	}

	var results []CheckResult
	for _, result := range allResults {
		if result.Status == status {
			results = append(results, result)
		}
	}

	return results, nil
}
