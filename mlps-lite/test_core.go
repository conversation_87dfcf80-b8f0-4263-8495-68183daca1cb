package main

import (
	"fmt"
	"log"
	"time"
)

func testCoreFeatures() {
	fmt.Println("=== Linux等保基线核查工具 - 核心功能测试 ===")
	
	storage := NewStorage()
	reporter := NewReportGenerator(storage)
	
	fmt.Println("\n1. 测试存储功能...")
	testStorage(storage)
	
	fmt.Println("\n2. 测试规则定义...")
	testRules()
	
	fmt.Println("\n3. 测试报告生成...")
	testReporter(storage, reporter)
	
	fmt.Println("\n=== 核心功能测试完成 ===")
}

func testStorage(storage *Storage) {
	testHost := Host{
		ID:       "test-host-1",
		Name:     "测试主机",
		IP:       "*************",
		Port:     22,
		Username: "root",
		Password: "password",
		OS:       "CentOS 7",
		Status:   "未连接",
	}
	
	err := storage.AddHost(testHost)
	if err != nil {
		log.Printf("添加主机失败: %v", err)
		return
	}
	fmt.Printf("✓ 成功添加测试主机: %s (%s)", testHost.Name, testHost.IP)
	
	hosts, err := storage.LoadHosts()
	if err != nil {
		log.Printf("加载主机失败: %v", err)
		return
	}
	fmt.Printf("✓ 成功加载主机列表，共 %d 台主机", len(hosts))
	
	testGroup := Group{
		ID:          "test-group-1",
		Name:        "测试分组",
		Description: "用于测试的主机分组",
		HostIDs:     []string{testHost.ID},
		CreatedAt:   time.Now().Format("2006-01-02 15:04:05"),
	}
	
	err = storage.AddGroup(testGroup)
	if err != nil {
		log.Printf("添加分组失败: %v", err)
		return
	}
	fmt.Printf("✓ 成功添加测试分组: %s", testGroup.Name)
	
	testResult := CheckResult{
		ID:          "test-result-1",
		HostID:      testHost.ID,
		HostIP:      testHost.IP,
		RuleID:      "rule_001",
		RuleName:    "密码最小长度检查",
		Status:      "PASS",
		Message:     "密码最小长度符合要求：8",
		Suggestion:  "",
		CheckedAt:   time.Now().Format("2006-01-02 15:04:05"),
		ExecutionID: "test-execution-1",
	}
	
	err = storage.AddResult(testResult)
	if err != nil {
		log.Printf("添加检查结果失败: %v", err)
		return
	}
	fmt.Printf("✓ 成功添加测试检查结果")
	
	testExecution := TaskExecution{
		ID:         "test-execution-1",
		Name:       "测试执行任务",
		HostIDs:    []string{testHost.ID},
		RuleIDs:    []string{"rule_001", "rule_002"},
		Status:     "COMPLETED",
		StartTime:  time.Now().Add(-5*time.Minute).Format("2006-01-02 15:04:05"),
		EndTime:    time.Now().Format("2006-01-02 15:04:05"),
		TotalHosts: 1,
		TotalRules: 2,
		PassCount:  1,
		FailCount:  1,
		ErrorCount: 0,
	}
	
	err = storage.AddExecution(testExecution)
	if err != nil {
		log.Printf("添加执行记录失败: %v", err)
		return
	}
	fmt.Printf("✓ 成功添加测试执行记录")
}

func testRules() {
	fmt.Printf("✓ 内置规则总数: %d", len(BuiltinRules))
	
	for i, rule := range BuiltinRules {
		if i < 3 {
			fmt.Printf("  - %s: %s (%s)", rule.ID, rule.Name, rule.Level)
		}
	}
	if len(BuiltinRules) > 3 {
		fmt.Printf("  ... 还有 %d 条规则", len(BuiltinRules)-3)
	}
	
	testChecker := GetRuleChecker("rule_001")
	if testChecker != nil {
		fmt.Printf("✓ 规则检查器加载成功: rule_001")
	} else {
		fmt.Printf("✗ 规则检查器加载失败: rule_001")
	}
}

func testReporter(storage *Storage, reporter *ReportGenerator) {
	executions, err := storage.LoadExecutions()
	if err != nil {
		log.Printf("加载执行记录失败: %v", err)
		return
	}
	
	if len(executions) == 0 {
		fmt.Printf("✓ 暂无执行记录，跳过报告生成测试")
		return
	}
	
	executionID := executions[0].ID
	
	txtFile, err := reporter.GenerateReport(executionID, "txt", ".")
	if err != nil {
		log.Printf("生成TXT报告失败: %v", err)
		return
	}
	fmt.Printf("✓ 成功生成TXT报告: %s", txtFile)
	
	csvFile, err := reporter.GenerateReport(executionID, "csv", ".")
	if err != nil {
		log.Printf("生成CSV报告失败: %v", err)
		return
	}
	fmt.Printf("✓ 成功生成CSV报告: %s", csvFile)
	
	summaryFile, err := reporter.GenerateReport(executionID, "summary", ".")
	if err != nil {
		log.Printf("生成汇总报告失败: %v", err)
		return
	}
	fmt.Printf("✓ 成功生成汇总报告: %s", summaryFile)
}

func main() {
	testCoreFeatures()
}
