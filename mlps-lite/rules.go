package main

import (
	"strconv"
	"strings"
)

type Rule struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Category    string `json:"category"`
	Level       string `json:"level"`
	Enabled     bool   `json:"enabled"`
}

type RuleChecker interface {
	Check(client *SSHClient) (bool, string, string, error)
}

var BuiltinRules = []Rule{
	{
		ID:          "rule_001",
		Name:        "密码最小长度检查",
		Description: "检查系统密码最小长度是否符合要求（≥8位）",
		Category:    "身份鉴别",
		Level:       "高",
		Enabled:     true,
	},
	{
		ID:          "rule_002",
		Name:        "root远程登录检查",
		Description: "检查是否禁止root用户远程登录",
		Category:    "访问控制",
		Level:       "高",
		Enabled:     true,
	},
	{
		ID:          "rule_003",
		Name:        "SSH密码登录检查",
		Description: "检查SSH是否禁用密码登录",
		Category:    "访问控制",
		Level:       "中",
		Enabled:     true,
	},
	{
		ID:          "rule_004",
		Name:        "审计服务检查",
		Description: "检查auditd审计服务是否启用",
		Category:    "安全审计",
		Level:       "高",
		Enabled:     true,
	},
	{
		ID:          "rule_005",
		Name:        "/etc/passwd权限检查",
		Description: "检查/etc/passwd文件权限是否为644",
		Category:    "访问控制",
		Level:       "高",
		Enabled:     true,
	},
	{
		ID:          "rule_006",
		Name:        "/etc/shadow权限检查",
		Description: "检查/etc/shadow文件权限是否为600或640",
		Category:    "访问控制",
		Level:       "高",
		Enabled:     true,
	},
	{
		ID:          "rule_007",
		Name:        "SELinux状态检查",
		Description: "检查SELinux是否启用",
		Category:    "访问控制",
		Level:       "中",
		Enabled:     true,
	},
	{
		ID:          "rule_008",
		Name:        "防火墙状态检查",
		Description: "检查防火墙是否启用",
		Category:    "边界防护",
		Level:       "高",
		Enabled:     true,
	},
	{
		ID:          "rule_009",
		Name:        "自动更新检查",
		Description: "检查是否启用自动更新",
		Category:    "恶意代码防范",
		Level:       "中",
		Enabled:     true,
	},
	{
		ID:          "rule_010",
		Name:        "日志目录权限检查",
		Description: "检查/var/log目录权限是否安全",
		Category:    "安全审计",
		Level:       "中",
		Enabled:     true,
	},
}

type PasswordLengthChecker struct{}

func (c *PasswordLengthChecker) Check(client *SSHClient) (bool, string, string, error) {
	minLen, err := client.GetPasswordPolicy("min_length")
	if err != nil {
		return false, "无法获取密码最小长度配置", "检查/etc/login.defs文件中PASS_MIN_LEN配置", err
	}

	if minLen == "" {
		return false, "未配置密码最小长度", "在/etc/login.defs中设置PASS_MIN_LEN 8", nil
	}

	length, err := strconv.Atoi(minLen)
	if err != nil {
		return false, "密码最小长度配置格式错误", "检查/etc/login.defs中PASS_MIN_LEN配置格式", nil
	}

	if length >= 8 {
		return true, "密码最小长度符合要求：" + minLen, "", nil
	}

	return false, "密码最小长度不足：" + minLen, "建议设置PASS_MIN_LEN为8或更大", nil
}

type RootRemoteLoginChecker struct{}

func (c *RootRemoteLoginChecker) Check(client *SSHClient) (bool, string, string, error) {
	permitRoot, err := client.GetConfigValue("/etc/ssh/sshd_config", "PermitRootLogin")
	if err != nil {
		return false, "无法读取SSH配置", "检查/etc/ssh/sshd_config文件权限", err
	}

	if permitRoot == "" || permitRoot == "yes" {
		return false, "允许root远程登录", "在/etc/ssh/sshd_config中设置PermitRootLogin no", nil
	}

	if permitRoot == "no" {
		return true, "已禁止root远程登录", "", nil
	}

	return false, "root远程登录配置异常：" + permitRoot, "建议设置PermitRootLogin no", nil
}

type SSHPasswordAuthChecker struct{}

func (c *SSHPasswordAuthChecker) Check(client *SSHClient) (bool, string, string, error) {
	passwordAuth := client.GetConfigValueWithDefault("/etc/ssh/sshd_config", "PasswordAuthentication", "yes")

	if passwordAuth == "no" {
		return true, "已禁用SSH密码登录", "", nil
	}

	return false, "SSH密码登录未禁用", "建议在/etc/ssh/sshd_config中设置PasswordAuthentication no", nil
}

type AuditdChecker struct{}

func (c *AuditdChecker) Check(client *SSHClient) (bool, string, string, error) {
	isActive, err := client.CheckService("auditd")
	if err != nil {
		return false, "无法检查auditd服务状态", "检查auditd服务是否安装", err
	}

	if !isActive {
		return false, "auditd服务未运行", "启动auditd服务：systemctl start auditd", nil
	}

	isEnabled, err := client.CheckServiceEnabled("auditd")
	if err != nil {
		return false, "无法检查auditd服务启用状态", "检查systemctl命令是否可用", err
	}

	if !isEnabled {
		return false, "auditd服务未设置开机启动", "设置auditd开机启动：systemctl enable auditd", nil
	}

	return true, "auditd服务正常运行且已设置开机启动", "", nil
}

type PasswdPermissionChecker struct{}

func (c *PasswdPermissionChecker) Check(client *SSHClient) (bool, string, string, error) {
	perm, err := client.GetFilePermissions("/etc/passwd")
	if err != nil {
		return false, "无法获取/etc/passwd文件权限", "检查文件是否存在", err
	}

	if perm == "644" {
		return true, "/etc/passwd文件权限正确：" + perm, "", nil
	}

	return false, "/etc/passwd文件权限异常：" + perm, "设置正确权限：chmod 644 /etc/passwd", nil
}

type ShadowPermissionChecker struct{}

func (c *ShadowPermissionChecker) Check(client *SSHClient) (bool, string, string, error) {
	perm, err := client.GetFilePermissions("/etc/shadow")
	if err != nil {
		return false, "无法获取/etc/shadow文件权限", "检查文件是否存在", err
	}

	if perm == "600" || perm == "640" {
		return true, "/etc/shadow文件权限正确：" + perm, "", nil
	}

	return false, "/etc/shadow文件权限异常：" + perm, "设置正确权限：chmod 600 /etc/shadow", nil
}

type SELinuxChecker struct{}

func (c *SELinuxChecker) Check(client *SSHClient) (bool, string, string, error) {
	status, err := client.GetSELinuxStatus()
	if err != nil {
		return false, "无法获取SELinux状态", "检查SELinux是否安装", err
	}

	status = strings.ToLower(status)
	if status == "enforcing" || status == "permissive" {
		return true, "SELinux已启用：" + status, "", nil
	}

	return false, "SELinux未启用：" + status, "启用SELinux：编辑/etc/selinux/config设置SELINUX=enforcing", nil
}

type FirewallChecker struct{}

func (c *FirewallChecker) Check(client *SSHClient) (bool, string, string, error) {
	firewallActive, _ := client.CheckService("firewalld")
	if firewallActive {
		return true, "firewalld防火墙已启用", "", nil
	}

	ufwActive, _ := client.CheckService("ufw")
	if ufwActive {
		return true, "ufw防火墙已启用", "", nil
	}

	iptablesOutput, err := client.ExecuteCommand("iptables -L | wc -l")
	if err == nil {
		lines, _ := strconv.Atoi(strings.TrimSpace(iptablesOutput))
		if lines > 8 {
			return true, "iptables防火墙规则已配置", "", nil
		}
	}

	return false, "防火墙未启用", "启用防火墙：systemctl start firewalld 或 ufw enable", nil
}

type AutoUpdateChecker struct{}

func (c *AutoUpdateChecker) Check(client *SSHClient) (bool, string, string, error) {
	yumCronActive, _ := client.CheckService("yum-cron")
	if yumCronActive {
		return true, "yum-cron自动更新已启用", "", nil
	}

	unattendedUpgrades, _ := client.CheckPackageInstalled("unattended-upgrades")
	if unattendedUpgrades {
		return true, "unattended-upgrades自动更新已安装", "", nil
	}

	return false, "自动更新未配置", "配置自动更新：安装yum-cron或unattended-upgrades", nil
}

type LogDirPermissionChecker struct{}

func (c *LogDirPermissionChecker) Check(client *SSHClient) (bool, string, string, error) {
	perm, err := client.GetFilePermissions("/var/log")
	if err != nil {
		return false, "无法获取/var/log目录权限", "检查目录是否存在", err
	}

	if perm == "755" || perm == "750" {
		return true, "/var/log目录权限正确：" + perm, "", nil
	}

	return false, "/var/log目录权限异常：" + perm, "设置正确权限：chmod 755 /var/log", nil
}

func GetRuleChecker(ruleID string) RuleChecker {
	switch ruleID {
	case "rule_001":
		return &PasswordLengthChecker{}
	case "rule_002":
		return &RootRemoteLoginChecker{}
	case "rule_003":
		return &SSHPasswordAuthChecker{}
	case "rule_004":
		return &AuditdChecker{}
	case "rule_005":
		return &PasswdPermissionChecker{}
	case "rule_006":
		return &ShadowPermissionChecker{}
	case "rule_007":
		return &SELinuxChecker{}
	case "rule_008":
		return &FirewallChecker{}
	case "rule_009":
		return &AutoUpdateChecker{}
	case "rule_010":
		return &LogDirPermissionChecker{}
	default:
		return nil
	}
}
