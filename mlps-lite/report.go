package main

import (
	"encoding/csv"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"
)

type ReportGenerator struct {
	storage *Storage
}

func NewReportGenerator(storage *Storage) *ReportGenerator {
	return &ReportGenerator{
		storage: storage,
	}
}

type ReportData struct {
	ExecutionID   string
	ExecutionName string
	GeneratedAt   string
	TotalHosts    int
	TotalRules    int
	PassCount     int
	FailCount     int
	ErrorCount    int
	Results       []CheckResult
	Hosts         map[string]*Host
	Rules         map[string]Rule
}

func (r *ReportGenerator) PrepareReportData(executionID string) (*ReportData, error) {
	executions, err := r.storage.LoadExecutions()
	if err != nil {
		return nil, fmt.Errorf("加载执行记录失败: %v", err)
	}

	var execution *TaskExecution
	for _, e := range executions {
		if e.ID == executionID {
			execution = &e
			break
		}
	}

	if execution == nil {
		return nil, fmt.Errorf("未找到执行记录: %s", executionID)
	}

	results, err := r.storage.LoadResults()
	if err != nil {
		return nil, fmt.Errorf("加载检查结果失败: %v", err)
	}

	var executionResults []CheckResult
	for _, result := range results {
		if result.ExecutionID == executionID {
			executionResults = append(executionResults, result)
		}
	}

	hosts, err := r.storage.LoadHosts()
	if err != nil {
		return nil, fmt.Errorf("加载主机信息失败: %v", err)
	}

	hostMap := make(map[string]*Host)
	for i, host := range hosts {
		hostMap[host.ID] = &hosts[i]
	}

	ruleMap := make(map[string]Rule)
	for _, rule := range BuiltinRules {
		ruleMap[rule.ID] = rule
	}

	reportData := &ReportData{
		ExecutionID:   executionID,
		ExecutionName: execution.Name,
		GeneratedAt:   time.Now().Format("2006-01-02 15:04:05"),
		TotalHosts:    execution.TotalHosts,
		TotalRules:    execution.TotalRules,
		PassCount:     execution.PassCount,
		FailCount:     execution.FailCount,
		ErrorCount:    execution.ErrorCount,
		Results:       executionResults,
		Hosts:         hostMap,
		Rules:         ruleMap,
	}

	return reportData, nil
}

func (r *ReportGenerator) ExportToTXT(executionID, filePath string) error {
	data, err := r.PrepareReportData(executionID)
	if err != nil {
		return err
	}

	file, err := os.Create(filePath)
	if err != nil {
		return fmt.Errorf("创建文件失败: %v", err)
	}
	defer file.Close()

	fmt.Fprintf(file, "Linux等保基线核查报告\n")
	fmt.Fprintf(file, "===========================================\n\n")
	fmt.Fprintf(file, "执行ID: %s\n", data.ExecutionID)
	fmt.Fprintf(file, "任务名称: %s\n", data.ExecutionName)
	fmt.Fprintf(file, "生成时间: %s\n", data.GeneratedAt)
	fmt.Fprintf(file, "检查主机数: %d\n", data.TotalHosts)
	fmt.Fprintf(file, "检查规则数: %d\n", data.TotalRules)
	fmt.Fprintf(file, "通过项目: %d\n", data.PassCount)
	fmt.Fprintf(file, "失败项目: %d\n", data.FailCount)
	fmt.Fprintf(file, "错误项目: %d\n", data.ErrorCount)
	fmt.Fprintf(file, "\n")

	fmt.Fprintf(file, "检查结果汇总\n")
	fmt.Fprintf(file, "===========================================\n\n")

	hostResults := make(map[string][]CheckResult)
	for _, result := range data.Results {
		hostResults[result.HostID] = append(hostResults[result.HostID], result)
	}

	for hostID, results := range hostResults {
		host := data.Hosts[hostID]
		if host == nil {
			continue
		}

		fmt.Fprintf(file, "主机: %s (%s)\n", host.Name, host.IP)
		fmt.Fprintf(file, "-------------------------------------------\n")

		for _, result := range results {
			rule := data.Rules[result.RuleID]
			statusSymbol := getStatusSymbol(result.Status)
			
			fmt.Fprintf(file, "%s [%s] %s\n", statusSymbol, result.Status, rule.Name)
			fmt.Fprintf(file, "   检查结果: %s\n", result.Message)
			if result.Suggestion != "" {
				fmt.Fprintf(file, "   建议措施: %s\n", result.Suggestion)
			}
			fmt.Fprintf(file, "   检查时间: %s\n", result.CheckedAt)
			fmt.Fprintf(file, "\n")
		}
		fmt.Fprintf(file, "\n")
	}

	fmt.Fprintf(file, "规则详情\n")
	fmt.Fprintf(file, "===========================================\n\n")

	for _, rule := range BuiltinRules {
		fmt.Fprintf(file, "规则ID: %s\n", rule.ID)
		fmt.Fprintf(file, "规则名称: %s\n", rule.Name)
		fmt.Fprintf(file, "规则描述: %s\n", rule.Description)
		fmt.Fprintf(file, "安全类别: %s\n", rule.Category)
		fmt.Fprintf(file, "风险等级: %s\n", rule.Level)
		fmt.Fprintf(file, "\n")
	}

	return nil
}

func (r *ReportGenerator) ExportToCSV(executionID, filePath string) error {
	data, err := r.PrepareReportData(executionID)
	if err != nil {
		return err
	}

	file, err := os.Create(filePath)
	if err != nil {
		return fmt.Errorf("创建文件失败: %v", err)
	}
	defer file.Close()

	writer := csv.NewWriter(file)
	defer writer.Flush()

	headers := []string{
		"主机名称",
		"主机IP",
		"规则ID",
		"规则名称",
		"规则类别",
		"风险等级",
		"检查状态",
		"检查结果",
		"建议措施",
		"检查时间",
	}

	err = writer.Write(headers)
	if err != nil {
		return fmt.Errorf("写入CSV头部失败: %v", err)
	}

	for _, result := range data.Results {
		host := data.Hosts[result.HostID]
		rule := data.Rules[result.RuleID]

		if host == nil {
			continue
		}

		record := []string{
			host.Name,
			host.IP,
			result.RuleID,
			rule.Name,
			rule.Category,
			rule.Level,
			result.Status,
			result.Message,
			result.Suggestion,
			result.CheckedAt,
		}

		err = writer.Write(record)
		if err != nil {
			return fmt.Errorf("写入CSV记录失败: %v", err)
		}
	}

	return nil
}

func (r *ReportGenerator) ExportSummaryToTXT(executionID, filePath string) error {
	data, err := r.PrepareReportData(executionID)
	if err != nil {
		return err
	}

	file, err := os.Create(filePath)
	if err != nil {
		return fmt.Errorf("创建文件失败: %v", err)
	}
	defer file.Close()

	fmt.Fprintf(file, "Linux等保基线核查汇总报告\n")
	fmt.Fprintf(file, "===========================================\n\n")
	fmt.Fprintf(file, "执行ID: %s\n", data.ExecutionID)
	fmt.Fprintf(file, "任务名称: %s\n", data.ExecutionName)
	fmt.Fprintf(file, "生成时间: %s\n", data.GeneratedAt)
	fmt.Fprintf(file, "\n")

	fmt.Fprintf(file, "检查统计\n")
	fmt.Fprintf(file, "-------------------------------------------\n")
	fmt.Fprintf(file, "检查主机数: %d\n", data.TotalHosts)
	fmt.Fprintf(file, "检查规则数: %d\n", data.TotalRules)
	fmt.Fprintf(file, "总检查项: %d\n", len(data.Results))
	fmt.Fprintf(file, "通过项目: %d (%.1f%%)\n", data.PassCount, float64(data.PassCount)/float64(len(data.Results))*100)
	fmt.Fprintf(file, "失败项目: %d (%.1f%%)\n", data.FailCount, float64(data.FailCount)/float64(len(data.Results))*100)
	fmt.Fprintf(file, "错误项目: %d (%.1f%%)\n", data.ErrorCount, float64(data.ErrorCount)/float64(len(data.Results))*100)
	fmt.Fprintf(file, "\n")

	hostStats := make(map[string]map[string]int)
	for _, result := range data.Results {
		if hostStats[result.HostID] == nil {
			hostStats[result.HostID] = make(map[string]int)
		}
		hostStats[result.HostID][result.Status]++
	}

	fmt.Fprintf(file, "主机检查统计\n")
	fmt.Fprintf(file, "-------------------------------------------\n")
	for hostID, stats := range hostStats {
		host := data.Hosts[hostID]
		if host == nil {
			continue
		}
		
		total := stats["PASS"] + stats["FAIL"] + stats["ERROR"]
		fmt.Fprintf(file, "%s (%s): 通过 %d, 失败 %d, 错误 %d (总计 %d)\n",
			host.Name, host.IP, stats["PASS"], stats["FAIL"], stats["ERROR"], total)
	}
	fmt.Fprintf(file, "\n")

	ruleStats := make(map[string]map[string]int)
	for _, result := range data.Results {
		if ruleStats[result.RuleID] == nil {
			ruleStats[result.RuleID] = make(map[string]int)
		}
		ruleStats[result.RuleID][result.Status]++
	}

	fmt.Fprintf(file, "规则检查统计\n")
	fmt.Fprintf(file, "-------------------------------------------\n")
	for ruleID, stats := range ruleStats {
		rule := data.Rules[ruleID]
		total := stats["PASS"] + stats["FAIL"] + stats["ERROR"]
		fmt.Fprintf(file, "%s: 通过 %d, 失败 %d, 错误 %d (总计 %d)\n",
			rule.Name, stats["PASS"], stats["FAIL"], stats["ERROR"], total)
	}

	return nil
}

func (r *ReportGenerator) GenerateReport(executionID, format, outputDir string) (string, error) {
	if outputDir == "" {
		outputDir = "."
	}

	err := os.MkdirAll(outputDir, 0755)
	if err != nil {
		return "", fmt.Errorf("创建输出目录失败: %v", err)
	}

	timestamp := time.Now().Format("20060102_150405")
	var fileName string
	var filePath string

	switch strings.ToLower(format) {
	case "txt":
		fileName = fmt.Sprintf("等保基线核查报告_%s.txt", timestamp)
		filePath = filepath.Join(outputDir, fileName)
		err = r.ExportToTXT(executionID, filePath)
	case "csv":
		fileName = fmt.Sprintf("等保基线核查报告_%s.csv", timestamp)
		filePath = filepath.Join(outputDir, fileName)
		err = r.ExportToCSV(executionID, filePath)
	case "summary":
		fileName = fmt.Sprintf("等保基线核查汇总_%s.txt", timestamp)
		filePath = filepath.Join(outputDir, fileName)
		err = r.ExportSummaryToTXT(executionID, filePath)
	default:
		return "", fmt.Errorf("不支持的报告格式: %s", format)
	}

	if err != nil {
		return "", err
	}

	return filePath, nil
}

func getStatusSymbol(status string) string {
	switch status {
	case "PASS":
		return "✓"
	case "FAIL":
		return "✗"
	case "ERROR":
		return "!"
	default:
		return "?"
	}
}
