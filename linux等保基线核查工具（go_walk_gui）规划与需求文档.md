# Linux 等保基线核查工具（Go · Walk GUI）需求文档（简化版 + 多主机）

> 版本：v0.2 · 作者：Sec/ChatGPT · 日期：2025-08-20

---

## 1. 项目目标
- 开发一款 **在 Windows 上运行** 的工具，通过 SSH 远程核查 Linux 主机的等保基线项。  
- 目标：**轻量、易用、支持多主机分组管理**。  
- 范围：支持批量主机核查，规则数量控制在 10–15 条常见高优先级项。

---

## 2. 功能需求

### 2.1 主机与分组管理
- 支持添加、编辑、删除主机：IP、端口、用户名、密码/私钥路径。  
- 支持主机分组：创建分组，将主机加入/移出分组。  
- 支持批量导入/导出主机（CSV/JSON）。  
- 提供连通性测试功能（批量 Ping/SSH 测试）。

### 2.2 核查规则（内置）
初版内置约 10 条规则：
1. 密码最小长度检查（/etc/login.defs）
2. root 是否禁止远程登录（/etc/ssh/sshd_config）
3. SSH 是否禁用密码登录
4. auditd 是否启用
5. /etc/passwd 权限检查
6. /etc/shadow 权限检查
7. SELinux 状态
8. 防火墙是否启用（firewalld/iptables）
9. 是否启用自动更新（yum/apt）
10. 日志目录权限是否安全

### 2.3 核查任务
- 支持选择 **单主机** 或 **主机组** 进行核查。  
- 支持批量并发执行（线程数可配置）。  
- 每条规则返回：**合格/不合格** + 简要说明。  

### 2.4 结果展示与导出
- GUI 表格展示结果：主机 | 规则名称 | 状态 | 建议。  
- 支持筛选（按主机/分组/规则/状态）。  
- 支持导出 TXT/CSV 报告，包含所有核查结果。

---

## 3. 非功能需求
- **简单易用**：操作尽量少，适合合规/运维人员直接使用。  
- **轻量运行**：无需数据库，主机/分组/结果信息保存到 JSON 文件。  
- **跨发行版兼容**：优先支持 CentOS/RHEL、Ubuntu/Debian。  
- **安全性**：SSH 凭据不落盘，仅运行时使用。

---

## 4. 界面设计（Walk GUI 简化）

### 4.1 导航栏（左侧）
1. 主机管理  
2. 主机分组  
3. 核查任务  
4. 核查结果  

### 4.2 页面布局
- **主机管理**：主机列表（IP、系统、状态、所属分组），按钮：新增/编辑/删除/导入/导出/测试连接。  
- **主机分组**：分组列表，支持拖拽或选择主机加入分组。  
- **核查任务**：选择基线规则集（默认内置）、选择主机/分组、设置并发，按钮：开始核查。  
- **核查结果**：表格展示结果，可筛选、导出报告。  

---

## 5. 技术选型
- 语言：Go 1.22+
- GUI 框架：Walk（Windows 原生 GUI）
- SSH：`golang.org/x/crypto/ssh`
- 存储：JSON 文件（hosts.json, groups.json, results.json）
- 报告导出：标准库（encoding/csv、os）

---

## 6. 目录结构（简化版 + 多主机）
```
mlps-lite/
  main.go         # GUI 启动入口
  sshclient.go    # SSH 连接与执行
  rules.go        # 内置规则定义
  checker.go      # 规则执行逻辑
  report.go       # 导出报告
  storage.go      # JSON 文件存储（主机、分组、结果）
```

---

## 7. 最小闭环流程
1. 用户添加主机/分组 → 测试连接  
2. 在核查任务中选择目标主机/分组 → 点击「开始核查」  
3. 工具通过 SSH 并发执行规则检查 → 收集结果  
4. 核查结果表格展示 → 用户可筛选并导出报告  

---

## 8. 后续扩展（可选）
- 报告支持 PDF 格式。  
- 规则库可配置/扩展。  
- 核查结果评分模型。  
- 定时任务与历史记录归档。

